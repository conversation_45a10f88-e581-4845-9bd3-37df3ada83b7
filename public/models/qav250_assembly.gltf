{"asset": {"generator": "SOLIDWORKSGLTF", "version": "2.0"}, "extensionsUsed": ["KHR_lights_punctual", "Solidworks_custom_properties", "KHR_draco_mesh_compression"], "extensionsRequired": ["KHR_draco_mesh_compression"], "scene": 0, "scenes": [{"name": "Default - Display State-1", "nodes": [0, 1]}], "nodes": [{"matrix": [-0.684937, 0.0137485, 0.728473, 0, 0.290508, 0.922063, 0.255744, 0, -0.668182, 0.386795, -0.635549, 0, -2.17218, 1.27738, -2.08209, 1], "camera": 0, "name": "current"}, {"children": [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80], "name": "QAV250 Assembly"}, {"matrix": [1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0.020955, 0.0205867, 0.14064, 1], "mesh": 0, "name": "QAV250 UPR PLATE SPACER-2"}, {"matrix": [1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, -0.020955, 0.0205867, 0.14064, 1], "mesh": 1, "name": "QAV250 UPR PLATE SPACER-4"}, {"matrix": [1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0.0380492, 0, 1], "mesh": 2, "name": "QAV250 TOP FRAME PLATE-1"}, {"matrix": [1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0.018034, 0.0205867, 0.00508, 1], "mesh": 3, "name": "QAV250 UPR PLATE SPACER-5"}, {"matrix": [1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, -0.018034, 0.0205867, 0.00508, 1], "mesh": 4, "name": "QAV250 UPR PLATE SPACER-6"}, {"matrix": [1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0.020955, -0.00508, 0.0606425, 1], "mesh": 5, "name": "QAV250 PWR DIST BRD SPACER-1"}, {"matrix": [1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0.020955, -0.00508, 0.14064, 1], "mesh": 6, "name": "QAV250 PWR DIST BRD SPACER-2"}, {"matrix": [1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, -0.020955, -0.00508, 0.0606425, 1], "mesh": 7, "name": "QAV250 PWR DIST BRD SPACER-3"}, {"matrix": [1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, -0.020955, -0.00508, 0.14064, 1], "mesh": 8, "name": "QAV250 PWR DIST BRD SPACER-4"}, {"matrix": [1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, -0.011684, 0, 1], "mesh": 9, "name": "QAV250 PWR DIST BRD-1"}, {"matrix": [1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0.020955, 0.0205867, 0.0606425, 1], "mesh": 10, "name": "QAV250 UPR PLATE SPACER-1"}, {"mesh": 11, "name": "QAV250 MAIN FRAME PLATE-1"}, {"matrix": [1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, -0.020955, 0.0205867, 0.0606425, 1], "mesh": 12, "name": "QAV250 UPR PLATE SPACER-3"}, {"matrix": [-0.866025, 0, -0.5, 0, 0, 1, 0, 0, 0.5, 0, -0.866025, 0, -0.0883783, 0.0031242, 0.0121492, 1], "mesh": 13, "name": "FXC1806-14 2300KV-2"}, {"matrix": [-1, 0, 0, 0, 0, 1, 0, 0, 0, 0, -1, 0, -0.0883883, 0.0316738, 0.0121666, 1], "mesh": 14, "name": "FXC1806-14 2300KV SPINNER-2"}, {"matrix": [-0.866025, 0, 0.5, 0, 0, 1, 0, 0, -0.5, 0, -0.866025, 0, -0.0883883, 0.0031242, 0.188943, 1], "mesh": 15, "name": "FXC1806-14 2300KV-4"}, {"matrix": [-1, 0, 0, 0, 0, 1, 0, 0, 0, 0, -1, 0, -0.0883883, 0.0316738, 0.188943, 1], "mesh": 16, "name": "FXC1806-14 2300KV SPINNER-3"}, {"matrix": [1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0.0205867, 0.00428625, 1], "name": "QAV250 CAMERA PLATE-1"}, {"matrix": [1.22465e-16, 0, 1, 0, 0, 1, 0, 0, -1, 0, 1.22465e-16, 0, -7.31935e-19, 0.0031242, 0.163868, 1], "mesh": 17, "name": "LUMENIER 1300 mAH 35C LIPO BATTERY-1"}, {"matrix": [0, -1, 0, 0, -1, 0, 1.66533e-16, 0, -1.66533e-16, 0, -1, 0, -0.018034, 0.040894, 0.00508, 1], "mesh": 18, "name": "socket button head cap screw_ai-1"}, {"matrix": [0, -1, 0, 0, -1, 0, 2.77556e-16, 0, -2.77556e-16, 0, -1, 0, -0.020955, 0.040894, 0.0606425, 1], "mesh": 19, "name": "socket button head cap screw_ai-2"}, {"matrix": [0, 1, 0, 0, -0.866025, 0, -0.5, 0, -0.5, 0, 0.866025, 0, 0.0913411, -0.0021844, 0.0172809, 1], "mesh": 20, "name": "socket head cap screw_ai-3"}, {"matrix": [0.866025, 0, -0.5, 0, 0, 1, 0, 0, 0.5, 0, 0.866025, 0, 0.0883883, 0.0031242, 0.0121666, 1], "mesh": 21, "name": "FXC1806-14 2300KV-1"}, {"matrix": [0, 1, 0, 0, -0.866025, 0, -0.5, 0, -0.5, 0, 0.866025, 0, 0.0854356, -0.0021844, 0.00705229, 1], "mesh": 22, "name": "socket head cap screw_ai-4"}, {"matrix": [0, -1, 0, 0, 1.22465e-16, 0, 1, 0, -1, 0, 1.22465e-16, 0, -0.01524, 0.0167386, 0.0858393, 1], "mesh": 23, "name": "pan cross head_ai-1"}, {"matrix": [1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0.01524, 0.0082042, 0.116091, 1], "mesh": 24, "name": "QAV250 CC3D SPACER-2"}, {"matrix": [0.866025, 0, -0.5, 0, 0.5, 0, 0.866025, 0, 0, -1, 0, 0, -0.01524, -0.0016764, 0.0858393, 1], "mesh": 25, "name": "machine screw nut hex_ai-1"}, {"matrix": [0.819152, 0, 0.573576, 0, 0, 1, 0, 0, -0.573576, 0, 0.819152, 0, 0.0883883, 0.0255524, 0.0121666, 1], "mesh": 26, "name": "GEMFAN 5030 PROP-1"}, {"matrix": [1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, -0.01524, 0.0082042, 0.116091, 1], "mesh": 27, "name": "QAV250 CC3D SPACER-4"}, {"matrix": [1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0.01524, 0.0082042, 0.0858393, 1], "mesh": 28, "name": "QAV250 CC3D SPACER-1"}, {"matrix": [-1.54894e-15, -3.54982e-15, -1, 0, 0.34202, 0.939693, -3.83027e-15, 0, 0.939693, -0.34202, -2.34033e-16, 0, 0.00782333, 0.0392537, 0.04064, 1], "mesh": 29, "name": "ANTENNA BRACE TUBE-1"}, {"matrix": [1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0.00254, 0.024384, 0.119278, 1], "mesh": 30, "name": "FAT SHARK FPV VIDEO TRANSMITTER-1"}, {"matrix": [2.22045e-16, 0, 1, 0, 0, -1, 0, 0, 1, 0, -2.22045e-16, 0, 2.99254e-16, 0.0380492, 0.0702437, 1], "mesh": 31, "name": "FUTABA R617FS RECEIVER-1"}, {"matrix": [0, -1, 0, 0, -1, 0, 1.66533e-16, 0, -1.66533e-16, 0, -1, 0, -0.020955, 0.040894, 0.14064, 1], "mesh": 32, "name": "socket button head cap screw_ai-3"}, {"matrix": [1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, -0.01524, 0.0082042, 0.0858393, 1], "mesh": 33, "name": "QAV250 CC3D SPACER-3"}, {"matrix": [1.22465e-16, 8.88178e-16, 1, 0, 1, 3.91409e-32, -1.22465e-16, 0, -1.60237e-31, 1, -1.22125e-15, 0, -3.82604e-18, 0.0031242, 0.0641858, 1], "mesh": 34, "name": "FAT SHARK FPV REGULATOR-1"}, {"matrix": [0, 1, 0, 0, -0.866025, 0, -0.5, 0, -0.5, 0, 0.866025, 0, 0.0815143, -0.0021844, 0.0161353, 1], "mesh": 35, "name": "socket head cap screw_ai-1"}, {"matrix": [1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, -1.04083e-17, 0.0132842, 0.101079, 1], "mesh": 36, "name": "CC3D FLIGHT CONTROLLER-1"}, {"matrix": [1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0.0883883, 0.0316738, 0.0121666, 1], "mesh": 37, "name": "FXC1806-14 2300KV SPINNER-1"}, {"matrix": [1.22465e-16, 0, 1, 0, 0, 1, 0, 0, -1, 0, 1.22465e-16, 0, -0.01016, -0.01016, 0.0790511, 1], "mesh": 38, "name": "LUMENIER 12A SIMONK ESC-1"}, {"matrix": [0, 1, 0, 0, -0.866025, 0, -0.5, 0, -0.5, 0, 0.866025, 0, 0.0952624, -0.0021844, 0.00819785, 1], "mesh": 39, "name": "socket head cap screw_ai-2"}, {"matrix": [0, -1, 0, 0, 1, 0, 2.77556e-16, 0, -2.77556e-16, 0, 1, 0, 0.020955, 0.040894, 0.0606425, 1], "mesh": 40, "name": "socket button head cap screw_ai-5"}, {"matrix": [0, -1, 0, 0, 1, 0, 1.66533e-16, 0, -1.66533e-16, 0, 1, 0, 0.018034, 0.040894, 0.00508, 1], "mesh": 41, "name": "socket button head cap screw_ai-6"}, {"matrix": [0, 1, 0, 0, 1, 0, 1.66533e-16, 0, 1.66533e-16, 0, -1, 0, 0.018034, -0.0013208, 0.00508, 1], "mesh": 42, "name": "socket button head cap screw_ai-9"}, {"matrix": [0, 1, 0, 0, -1, 0, 1.66533e-16, 0, 1.66533e-16, 0, 1, 0, -0.018034, -0.0013208, 0.00508, 1], "mesh": 43, "name": "socket button head cap screw_ai-10"}, {"matrix": [0.866025, 0, 0.5, 0, 0, 1, 0, 0, -0.5, 0, 0.866025, 0, 0.0883783, 0.0031242, 0.188961, 1], "mesh": 44, "name": "FXC1806-14 2300KV-5"}, {"matrix": [-1.22465e-16, 0, 1, 0, 0, 1, 0, 0, -1, 0, -1.22465e-16, 0, 0.01016, -0.01016, 0.0790511, 1], "mesh": 45, "name": "LUMENIER 12A SIMONK ESC-2"}, {"matrix": [-1.22465e-16, 0, -1, 0, 0, 1, 0, 0, 1, 0, -1.22465e-16, 0, 0.01016, -0.01016, 0.122231, 1], "mesh": 46, "name": "LUMENIER 12A SIMONK ESC-3"}, {"matrix": [1.22465e-16, 0, -1, 0, 0, 1, 0, 0, 1, 0, 1.22465e-16, 0, -0.01016, -0.01016, 0.122231, 1], "mesh": 47, "name": "LUMENIER 12A SIMONK ESC-4"}, {"matrix": [1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0.0883883, 0.0316738, 0.188943, 1], "mesh": 48, "name": "FXC1806-14 2300KV SPINNER-4"}, {"matrix": [1.10486e-15, -3.54982e-15, -1, 0, -0.34202, 0.939693, -3.71362e-15, 0, 0.939693, 0.34202, -1.75885e-16, 0, -0.00782333, 0.0392537, 0.04064, 1], "mesh": 49, "name": "ANTENNA BRACE TUBE-3"}, {"matrix": [0, -1, 0, 0, 1, 0, 1.66533e-16, 0, -1.66533e-16, 0, 1, 0, 0.020955, 0.040894, 0.14064, 1], "mesh": 50, "name": "socket button head cap screw_ai-4"}, {"matrix": [0, 1, 0, 0, 0.866025, 0, 0.5, 0, 0.5, 0, -0.866025, 0, -0.0815143, -0.0021844, 0.184975, 1], "mesh": 51, "name": "socket head cap screw_ai-9"}, {"matrix": [0, 1, 0, 0, 1, 0, 2.77556e-16, 0, 2.77556e-16, 0, -1, 0, 0.020955, -0.0130048, 0.0606425, 1], "mesh": 52, "name": "socket button head cap screw_ai-13"}, {"matrix": [0, 1, 0, 0, -1, 0, 2.77556e-16, 0, 2.77556e-16, 0, 1, 0, -0.020955, -0.0130048, 0.0606425, 1], "mesh": 53, "name": "socket button head cap screw_ai-14"}, {"matrix": [0, 1, 0, 0, 0.866025, 0, -0.5, 0, -0.5, 0, -0.866025, 0, -0.0952624, -0.0021844, 0.00819785, 1], "mesh": 54, "name": "socket head cap screw_ai-7"}, {"matrix": [0, 1, 0, 0, 1, 0, 1.66533e-16, 0, 1.66533e-16, 0, -1, 0, 0.020955, -0.0130048, 0.14064, 1], "mesh": 55, "name": "socket button head cap screw_ai-15"}, {"matrix": [0, 1, 0, 0, -1, 0, 1.66533e-16, 0, 1.66533e-16, 0, 1, 0, -0.020955, -0.0130048, 0.14064, 1], "mesh": 56, "name": "socket button head cap screw_ai-16"}, {"matrix": [0, 1, 0, 0, 0.866025, 0, -0.5, 0, -0.5, 0, -0.866025, 0, -0.0854356, -0.0021844, 0.00705229, 1], "mesh": 57, "name": "socket head cap screw_ai-8"}, {"matrix": [0, 1, 0, 0, 0.866025, 0, 0.5, 0, 0.5, 0, -0.866025, 0, -0.0913411, -0.0021844, 0.183829, 1], "mesh": 58, "name": "socket head cap screw_ai-10"}, {"matrix": [0, 1, 0, 0, 0.866025, 0, 0.5, 0, 0.5, 0, -0.866025, 0, -0.0952624, -0.0021844, 0.192912, 1], "mesh": 59, "name": "socket head cap screw_ai-11"}, {"matrix": [0, 1, 0, 0, 0.866025, 0, -0.5, 0, -0.5, 0, -0.866025, 0, -0.0913411, -0.0021844, 0.0172809, 1], "mesh": 60, "name": "socket head cap screw_ai-6"}, {"matrix": [0, 1, 0, 0, 0.866025, 0, -0.5, 0, -0.5, 0, -0.866025, 0, -0.0815143, -0.0021844, 0.0161353, 1], "mesh": 61, "name": "socket head cap screw_ai-5"}, {"matrix": [0, 1, 0, 0, -0.866025, 0, 0.5, 0, 0.5, 0, 0.866025, 0, 0.0913411, -0.0021844, 0.183829, 1], "mesh": 62, "name": "socket head cap screw_ai-14"}, {"matrix": [0, 1, 0, 0, -0.866025, 0, 0.5, 0, 0.5, 0, 0.866025, 0, 0.0854356, -0.0021844, 0.194058, 1], "mesh": 63, "name": "socket head cap screw_ai-16"}, {"matrix": [0, -1, 0, 0, 1.22465e-16, 0, -1, 0, 1, 0, 1.22465e-16, 0, -0.01524, 0.0167386, 0.116319, 1], "mesh": 64, "name": "pan cross head_ai-2"}, {"matrix": [-0.866025, 0, 0.5, 0, -0.5, 0, -0.866025, 0, 0, -1, 0, 0, 0.01524, -0.0016764, 0.116319, 1], "mesh": 65, "name": "machine screw nut hex_ai-3"}, {"matrix": [0, -1, 0, 0, -1.22465e-16, 0, 1, 0, -1, 0, -1.22465e-16, 0, 0.01524, 0.0167386, 0.0858393, 1], "mesh": 66, "name": "pan cross head_ai-4"}, {"matrix": [0, -1, 0, 0, -1.22465e-16, 0, -1, 0, 1, 0, -1.22465e-16, 0, 0.01524, 0.0167386, 0.116319, 1], "mesh": 67, "name": "pan cross head_ai-3"}, {"matrix": [-0.866025, 0, -0.5, 0, -0.5, 0, 0.866025, 0, 0, 1, 0, 0, 0.01524, -2.21577e-06, 0.0858393, 1], "mesh": 68, "name": "machine screw nut hex_ai-4"}, {"matrix": [0.866025, 0, 0.5, 0, 0.5, 0, -0.866025, 0, 0, 1, 0, 0, -0.01524, -2.21577e-06, 0.116319, 1], "mesh": 69, "name": "machine screw nut hex_ai-2"}, {"matrix": [0, 1, 0, 0, 0.866025, 0, 0.5, 0, 0.5, 0, -0.866025, 0, -0.0854356, -0.0021844, 0.194058, 1], "mesh": 70, "name": "socket head cap screw_ai-12"}, {"matrix": [0, 1, 0, 0, -0.866025, 0, 0.5, 0, 0.5, 0, 0.866025, 0, 0.0952624, -0.0021844, 0.192912, 1], "mesh": 71, "name": "socket head cap screw_ai-15"}, {"matrix": [0, 1, 0, 0, -0.866025, 0, 0.5, 0, 0.5, 0, 0.866025, 0, 0.0815143, -0.0021844, 0.184975, 1], "mesh": 72, "name": "socket head cap screw_ai-13"}, {"matrix": [0.819152, 0, -0.573576, 0, 0, -1, 0, 0, -0.573576, 0, -0.819152, 0, 0.0883883, 0.0325621, 0.188943, 1], "mesh": 73, "name": "GEMFAN 5030 PROP-2"}, {"matrix": [-0.819152, 0, 0.573576, 0, 0, -1, 0, 0, 0.573576, 0, 0.819152, 0, -0.0883883, 0.0325621, 0.0121666, 1], "mesh": 74, "name": "GEMFAN 5030 PROP-3"}, {"matrix": [-0.819152, 0, -0.573576, 0, 0, 1, 0, 0, 0.573576, 0, -0.819152, 0, -0.0883883, 0.0255524, 0.188943, 1], "mesh": 75, "name": "GEMFAN 5030 PROP-4"}, {"mesh": 76, "name": "50-0041-0001-05-1"}, {"matrix": [-1, -6.79641e-17, -9.24446e-33, 0, -1.92593e-34, 8.32667e-17, -1, 0, 6.79641e-17, -1, -8.32667e-17, 0, 9.41385e-36, 0.0205867, 0.0159702, 1], "mesh": 77, "name": "600TVL Fatshark Camera-1"}], "cameras": [{"type": "orthographic", "orthographic": {"xmag": 1, "ymag": 1, "zfar": 100, "znear": 0.01}}], "meshes": [{"primitives": [{"attributes": {"POSITION": 1, "NORMAL": 2, "TEXCOORD_0": 3}, "indices": 0, "mode": 4, "material": 4, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 0, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}]}, {"primitives": [{"attributes": {"POSITION": 5, "NORMAL": 6, "TEXCOORD_0": 7}, "indices": 4, "mode": 4, "material": 4, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 1, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}]}, {"primitives": [{"attributes": {"POSITION": 9, "NORMAL": 10, "TEXCOORD_0": 11}, "indices": 8, "mode": 4, "material": 5, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 2, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}]}, {"primitives": [{"attributes": {"POSITION": 13, "NORMAL": 14, "TEXCOORD_0": 15}, "indices": 12, "mode": 4, "material": 4, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 3, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}]}, {"primitives": [{"attributes": {"POSITION": 17, "NORMAL": 18, "TEXCOORD_0": 19}, "indices": 16, "mode": 4, "material": 4, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 4, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}]}, {"primitives": [{"attributes": {"POSITION": 21, "NORMAL": 22, "TEXCOORD_0": 23}, "indices": 20, "mode": 4, "material": 6, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 5, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}]}, {"primitives": [{"attributes": {"POSITION": 25, "NORMAL": 26, "TEXCOORD_0": 27}, "indices": 24, "mode": 4, "material": 6, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 6, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}]}, {"primitives": [{"attributes": {"POSITION": 29, "NORMAL": 30, "TEXCOORD_0": 31}, "indices": 28, "mode": 4, "material": 6, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 7, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}]}, {"primitives": [{"attributes": {"POSITION": 33, "NORMAL": 34, "TEXCOORD_0": 35}, "indices": 32, "mode": 4, "material": 6, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 8, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}]}, {"primitives": [{"attributes": {"POSITION": 37, "NORMAL": 38, "TEXCOORD_0": 39}, "indices": 36, "mode": 4, "material": 7, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 9, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}]}, {"primitives": [{"attributes": {"POSITION": 41, "NORMAL": 42, "TEXCOORD_0": 43}, "indices": 40, "mode": 4, "material": 4, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 10, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}]}, {"primitives": [{"attributes": {"POSITION": 45, "NORMAL": 46, "TEXCOORD_0": 47}, "indices": 44, "mode": 4, "material": 8, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 11, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}]}, {"primitives": [{"attributes": {"POSITION": 49, "NORMAL": 50, "TEXCOORD_0": 51}, "indices": 48, "mode": 4, "material": 4, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 12, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}]}, {"primitives": [{"attributes": {"POSITION": 53, "NORMAL": 54, "TEXCOORD_0": 55}, "indices": 52, "mode": 4, "material": 9, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 13, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}, {"attributes": {"POSITION": 57, "NORMAL": 58, "TEXCOORD_0": 59}, "indices": 56, "mode": 4, "material": 10, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 14, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}, {"attributes": {"POSITION": 61, "NORMAL": 62, "TEXCOORD_0": 63}, "indices": 60, "mode": 4, "material": 11, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 15, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}, {"attributes": {"POSITION": 65, "NORMAL": 66, "TEXCOORD_0": 67}, "indices": 64, "mode": 4, "material": 11, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 16, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}, {"attributes": {"POSITION": 69, "NORMAL": 70, "TEXCOORD_0": 71}, "indices": 68, "mode": 4, "material": 11, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 17, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}, {"attributes": {"POSITION": 73, "NORMAL": 74, "TEXCOORD_0": 75}, "indices": 72, "mode": 4, "material": 11, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 18, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}, {"attributes": {"POSITION": 77, "NORMAL": 78, "TEXCOORD_0": 79}, "indices": 76, "mode": 4, "material": 11, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 19, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}, {"attributes": {"POSITION": 81, "NORMAL": 82, "TEXCOORD_0": 83}, "indices": 80, "mode": 4, "material": 11, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 20, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}, {"attributes": {"POSITION": 85, "NORMAL": 86, "TEXCOORD_0": 87}, "indices": 84, "mode": 4, "material": 11, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 21, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}, {"attributes": {"POSITION": 89, "NORMAL": 90, "TEXCOORD_0": 91}, "indices": 88, "mode": 4, "material": 12, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 22, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}]}, {"primitives": [{"attributes": {"POSITION": 93, "NORMAL": 94, "TEXCOORD_0": 95}, "indices": 92, "mode": 4, "material": 13, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 23, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}]}, {"primitives": [{"attributes": {"POSITION": 97, "NORMAL": 98, "TEXCOORD_0": 99}, "indices": 96, "mode": 4, "material": 9, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 24, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}, {"attributes": {"POSITION": 101, "NORMAL": 102, "TEXCOORD_0": 103}, "indices": 100, "mode": 4, "material": 10, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 25, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}, {"attributes": {"POSITION": 105, "NORMAL": 106, "TEXCOORD_0": 107}, "indices": 104, "mode": 4, "material": 11, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 26, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}, {"attributes": {"POSITION": 109, "NORMAL": 110, "TEXCOORD_0": 111}, "indices": 108, "mode": 4, "material": 11, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 27, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}, {"attributes": {"POSITION": 113, "NORMAL": 114, "TEXCOORD_0": 115}, "indices": 112, "mode": 4, "material": 11, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 28, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}, {"attributes": {"POSITION": 117, "NORMAL": 118, "TEXCOORD_0": 119}, "indices": 116, "mode": 4, "material": 11, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 29, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}, {"attributes": {"POSITION": 121, "NORMAL": 122, "TEXCOORD_0": 123}, "indices": 120, "mode": 4, "material": 11, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 30, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}, {"attributes": {"POSITION": 125, "NORMAL": 126, "TEXCOORD_0": 127}, "indices": 124, "mode": 4, "material": 11, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 31, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}, {"attributes": {"POSITION": 129, "NORMAL": 130, "TEXCOORD_0": 131}, "indices": 128, "mode": 4, "material": 11, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 32, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}, {"attributes": {"POSITION": 133, "NORMAL": 134, "TEXCOORD_0": 135}, "indices": 132, "mode": 4, "material": 12, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 33, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}]}, {"primitives": [{"attributes": {"POSITION": 137, "NORMAL": 138, "TEXCOORD_0": 139}, "indices": 136, "mode": 4, "material": 13, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 34, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}]}, {"primitives": [{"attributes": {"POSITION": 141, "NORMAL": 142, "TEXCOORD_0": 143}, "indices": 140, "mode": 4, "material": 14, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 35, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}, {"attributes": {"POSITION": 145, "NORMAL": 146, "TEXCOORD_0": 147}, "indices": 144, "mode": 4, "material": 15, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 36, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}, {"attributes": {"POSITION": 149, "NORMAL": 150, "TEXCOORD_0": 151}, "indices": 148, "mode": 4, "material": 16, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 37, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}]}, {"primitives": [{"attributes": {"POSITION": 153, "NORMAL": 154, "TEXCOORD_0": 155}, "indices": 152, "mode": 4, "material": 17, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 38, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}]}, {"primitives": [{"attributes": {"POSITION": 157, "NORMAL": 158, "TEXCOORD_0": 159}, "indices": 156, "mode": 4, "material": 17, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 39, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}]}, {"primitives": [{"attributes": {"POSITION": 161, "NORMAL": 162, "TEXCOORD_0": 163}, "indices": 160, "mode": 4, "material": 18, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 40, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}]}, {"primitives": [{"attributes": {"POSITION": 165, "NORMAL": 166, "TEXCOORD_0": 167}, "indices": 164, "mode": 4, "material": 9, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 41, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}, {"attributes": {"POSITION": 169, "NORMAL": 170, "TEXCOORD_0": 171}, "indices": 168, "mode": 4, "material": 10, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 42, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}, {"attributes": {"POSITION": 173, "NORMAL": 174, "TEXCOORD_0": 175}, "indices": 172, "mode": 4, "material": 11, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 43, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}, {"attributes": {"POSITION": 177, "NORMAL": 178, "TEXCOORD_0": 179}, "indices": 176, "mode": 4, "material": 11, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 44, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}, {"attributes": {"POSITION": 181, "NORMAL": 182, "TEXCOORD_0": 183}, "indices": 180, "mode": 4, "material": 11, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 45, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}, {"attributes": {"POSITION": 185, "NORMAL": 186, "TEXCOORD_0": 187}, "indices": 184, "mode": 4, "material": 11, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 46, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}, {"attributes": {"POSITION": 189, "NORMAL": 190, "TEXCOORD_0": 191}, "indices": 188, "mode": 4, "material": 11, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 47, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}, {"attributes": {"POSITION": 193, "NORMAL": 194, "TEXCOORD_0": 195}, "indices": 192, "mode": 4, "material": 11, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 48, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}, {"attributes": {"POSITION": 197, "NORMAL": 198, "TEXCOORD_0": 199}, "indices": 196, "mode": 4, "material": 11, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 49, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}, {"attributes": {"POSITION": 201, "NORMAL": 202, "TEXCOORD_0": 203}, "indices": 200, "mode": 4, "material": 12, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 50, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}]}, {"primitives": [{"attributes": {"POSITION": 205, "NORMAL": 206, "TEXCOORD_0": 207}, "indices": 204, "mode": 4, "material": 18, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 51, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}]}, {"primitives": [{"attributes": {"POSITION": 209, "NORMAL": 210, "TEXCOORD_0": 211}, "indices": 208, "mode": 4, "material": 19, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 52, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}]}, {"primitives": [{"attributes": {"POSITION": 213, "NORMAL": 214, "TEXCOORD_0": 215}, "indices": 212, "mode": 4, "material": 20, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 53, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}]}, {"primitives": [{"attributes": {"POSITION": 217, "NORMAL": 218, "TEXCOORD_0": 219}, "indices": 216, "mode": 4, "material": 21, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 54, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}]}, {"primitives": [{"attributes": {"POSITION": 221, "NORMAL": 222, "TEXCOORD_0": 223}, "indices": 220, "mode": 4, "material": 22, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 55, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}]}, {"primitives": [{"attributes": {"POSITION": 225, "NORMAL": 226, "TEXCOORD_0": 227}, "indices": 224, "mode": 4, "material": 20, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 56, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}]}, {"primitives": [{"attributes": {"POSITION": 229, "NORMAL": 230, "TEXCOORD_0": 231}, "indices": 228, "mode": 4, "material": 20, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 57, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}]}, {"primitives": [{"attributes": {"POSITION": 233, "NORMAL": 234, "TEXCOORD_0": 235}, "indices": 232, "mode": 4, "material": 23, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 58, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}]}, {"primitives": [{"attributes": {"POSITION": 237, "NORMAL": 238, "TEXCOORD_0": 239}, "indices": 236, "mode": 4, "material": 24, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 59, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}, {"attributes": {"POSITION": 241, "NORMAL": 242, "TEXCOORD_0": 243}, "indices": 240, "mode": 4, "material": 25, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 60, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}]}, {"primitives": [{"attributes": {"POSITION": 245, "NORMAL": 246, "TEXCOORD_0": 247}, "indices": 244, "mode": 4, "material": 26, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 61, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}, {"attributes": {"POSITION": 249, "NORMAL": 250, "TEXCOORD_0": 251}, "indices": 248, "mode": 4, "material": 27, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 62, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}, {"attributes": {"POSITION": 253, "NORMAL": 254, "TEXCOORD_0": 255}, "indices": 252, "mode": 4, "material": 28, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 63, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}]}, {"primitives": [{"attributes": {"POSITION": 257, "NORMAL": 258, "TEXCOORD_0": 259}, "indices": 256, "mode": 4, "material": 17, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 64, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}]}, {"primitives": [{"attributes": {"POSITION": 261, "NORMAL": 262, "TEXCOORD_0": 263}, "indices": 260, "mode": 4, "material": 20, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 65, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}]}, {"primitives": [{"attributes": {"POSITION": 265, "NORMAL": 266, "TEXCOORD_0": 267}, "indices": 264, "mode": 4, "material": 29, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 66, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}]}, {"primitives": [{"attributes": {"POSITION": 269, "NORMAL": 270, "TEXCOORD_0": 271}, "indices": 268, "mode": 4, "material": 18, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 67, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}]}, {"primitives": [{"attributes": {"POSITION": 273, "NORMAL": 274, "TEXCOORD_0": 275}, "indices": 272, "mode": 4, "material": 30, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 68, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}, {"attributes": {"POSITION": 277, "NORMAL": 278, "TEXCOORD_0": 279}, "indices": 276, "mode": 4, "material": 31, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 69, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}, {"attributes": {"POSITION": 281, "NORMAL": 282, "TEXCOORD_0": 283}, "indices": 280, "mode": 4, "material": 32, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 70, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}, {"attributes": {"POSITION": 285, "NORMAL": 286, "TEXCOORD_0": 287}, "indices": 284, "mode": 4, "material": 33, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 71, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}, {"attributes": {"POSITION": 289, "NORMAL": 290, "TEXCOORD_0": 291}, "indices": 288, "mode": 4, "material": 34, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 72, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}]}, {"primitives": [{"attributes": {"POSITION": 293, "NORMAL": 294, "TEXCOORD_0": 295}, "indices": 292, "mode": 4, "material": 13, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 73, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}]}, {"primitives": [{"attributes": {"POSITION": 297, "NORMAL": 298, "TEXCOORD_0": 299}, "indices": 296, "mode": 4, "material": 35, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 74, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}, {"attributes": {"POSITION": 301, "NORMAL": 302, "TEXCOORD_0": 303}, "indices": 300, "mode": 4, "material": 36, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 75, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}]}, {"primitives": [{"attributes": {"POSITION": 305, "NORMAL": 306, "TEXCOORD_0": 307}, "indices": 304, "mode": 4, "material": 18, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 76, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}]}, {"primitives": [{"attributes": {"POSITION": 309, "NORMAL": 310, "TEXCOORD_0": 311}, "indices": 308, "mode": 4, "material": 17, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 77, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}]}, {"primitives": [{"attributes": {"POSITION": 313, "NORMAL": 314, "TEXCOORD_0": 315}, "indices": 312, "mode": 4, "material": 17, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 78, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}]}, {"primitives": [{"attributes": {"POSITION": 317, "NORMAL": 318, "TEXCOORD_0": 319}, "indices": 316, "mode": 4, "material": 17, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 79, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}]}, {"primitives": [{"attributes": {"POSITION": 321, "NORMAL": 322, "TEXCOORD_0": 323}, "indices": 320, "mode": 4, "material": 17, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 80, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}]}, {"primitives": [{"attributes": {"POSITION": 325, "NORMAL": 326, "TEXCOORD_0": 327}, "indices": 324, "mode": 4, "material": 9, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 81, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}, {"attributes": {"POSITION": 329, "NORMAL": 330, "TEXCOORD_0": 331}, "indices": 328, "mode": 4, "material": 10, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 82, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}, {"attributes": {"POSITION": 333, "NORMAL": 334, "TEXCOORD_0": 335}, "indices": 332, "mode": 4, "material": 11, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 83, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}, {"attributes": {"POSITION": 337, "NORMAL": 338, "TEXCOORD_0": 339}, "indices": 336, "mode": 4, "material": 11, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 84, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}, {"attributes": {"POSITION": 341, "NORMAL": 342, "TEXCOORD_0": 343}, "indices": 340, "mode": 4, "material": 11, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 85, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}, {"attributes": {"POSITION": 345, "NORMAL": 346, "TEXCOORD_0": 347}, "indices": 344, "mode": 4, "material": 11, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 86, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}, {"attributes": {"POSITION": 349, "NORMAL": 350, "TEXCOORD_0": 351}, "indices": 348, "mode": 4, "material": 11, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 87, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}, {"attributes": {"POSITION": 353, "NORMAL": 354, "TEXCOORD_0": 355}, "indices": 352, "mode": 4, "material": 11, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 88, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}, {"attributes": {"POSITION": 357, "NORMAL": 358, "TEXCOORD_0": 359}, "indices": 356, "mode": 4, "material": 11, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 89, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}, {"attributes": {"POSITION": 361, "NORMAL": 362, "TEXCOORD_0": 363}, "indices": 360, "mode": 4, "material": 12, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 90, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}]}, {"primitives": [{"attributes": {"POSITION": 365, "NORMAL": 366, "TEXCOORD_0": 367}, "indices": 364, "mode": 4, "material": 35, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 91, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}, {"attributes": {"POSITION": 369, "NORMAL": 370, "TEXCOORD_0": 371}, "indices": 368, "mode": 4, "material": 36, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 92, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}]}, {"primitives": [{"attributes": {"POSITION": 373, "NORMAL": 374, "TEXCOORD_0": 375}, "indices": 372, "mode": 4, "material": 35, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 93, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}, {"attributes": {"POSITION": 377, "NORMAL": 378, "TEXCOORD_0": 379}, "indices": 376, "mode": 4, "material": 36, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 94, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}]}, {"primitives": [{"attributes": {"POSITION": 381, "NORMAL": 382, "TEXCOORD_0": 383}, "indices": 380, "mode": 4, "material": 35, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 95, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}, {"attributes": {"POSITION": 385, "NORMAL": 386, "TEXCOORD_0": 387}, "indices": 384, "mode": 4, "material": 36, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 96, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}]}, {"primitives": [{"attributes": {"POSITION": 389, "NORMAL": 390, "TEXCOORD_0": 391}, "indices": 388, "mode": 4, "material": 13, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 97, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}]}, {"primitives": [{"attributes": {"POSITION": 393, "NORMAL": 394, "TEXCOORD_0": 395}, "indices": 392, "mode": 4, "material": 23, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 98, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}]}, {"primitives": [{"attributes": {"POSITION": 397, "NORMAL": 398, "TEXCOORD_0": 399}, "indices": 396, "mode": 4, "material": 17, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 99, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}]}, {"primitives": [{"attributes": {"POSITION": 401, "NORMAL": 402, "TEXCOORD_0": 403}, "indices": 400, "mode": 4, "material": 18, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 100, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}]}, {"primitives": [{"attributes": {"POSITION": 405, "NORMAL": 406, "TEXCOORD_0": 407}, "indices": 404, "mode": 4, "material": 17, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 101, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}]}, {"primitives": [{"attributes": {"POSITION": 409, "NORMAL": 410, "TEXCOORD_0": 411}, "indices": 408, "mode": 4, "material": 17, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 102, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}]}, {"primitives": [{"attributes": {"POSITION": 413, "NORMAL": 414, "TEXCOORD_0": 415}, "indices": 412, "mode": 4, "material": 18, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 103, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}]}, {"primitives": [{"attributes": {"POSITION": 417, "NORMAL": 418, "TEXCOORD_0": 419}, "indices": 416, "mode": 4, "material": 17, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 104, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}]}, {"primitives": [{"attributes": {"POSITION": 421, "NORMAL": 422, "TEXCOORD_0": 423}, "indices": 420, "mode": 4, "material": 17, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 105, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}]}, {"primitives": [{"attributes": {"POSITION": 425, "NORMAL": 426, "TEXCOORD_0": 427}, "indices": 424, "mode": 4, "material": 18, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 106, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}]}, {"primitives": [{"attributes": {"POSITION": 429, "NORMAL": 430, "TEXCOORD_0": 431}, "indices": 428, "mode": 4, "material": 18, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 107, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}]}, {"primitives": [{"attributes": {"POSITION": 433, "NORMAL": 434, "TEXCOORD_0": 435}, "indices": 432, "mode": 4, "material": 18, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 108, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}]}, {"primitives": [{"attributes": {"POSITION": 437, "NORMAL": 438, "TEXCOORD_0": 439}, "indices": 436, "mode": 4, "material": 18, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 109, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}]}, {"primitives": [{"attributes": {"POSITION": 441, "NORMAL": 442, "TEXCOORD_0": 443}, "indices": 440, "mode": 4, "material": 18, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 110, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}]}, {"primitives": [{"attributes": {"POSITION": 445, "NORMAL": 446, "TEXCOORD_0": 447}, "indices": 444, "mode": 4, "material": 18, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 111, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}]}, {"primitives": [{"attributes": {"POSITION": 449, "NORMAL": 450, "TEXCOORD_0": 451}, "indices": 448, "mode": 4, "material": 18, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 112, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}]}, {"primitives": [{"attributes": {"POSITION": 453, "NORMAL": 454, "TEXCOORD_0": 455}, "indices": 452, "mode": 4, "material": 19, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 113, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}]}, {"primitives": [{"attributes": {"POSITION": 457, "NORMAL": 458, "TEXCOORD_0": 459}, "indices": 456, "mode": 4, "material": 21, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 114, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}]}, {"primitives": [{"attributes": {"POSITION": 461, "NORMAL": 462, "TEXCOORD_0": 463}, "indices": 460, "mode": 4, "material": 19, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 115, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}]}, {"primitives": [{"attributes": {"POSITION": 465, "NORMAL": 466, "TEXCOORD_0": 467}, "indices": 464, "mode": 4, "material": 19, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 116, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}]}, {"primitives": [{"attributes": {"POSITION": 469, "NORMAL": 470, "TEXCOORD_0": 471}, "indices": 468, "mode": 4, "material": 21, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 117, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}]}, {"primitives": [{"attributes": {"POSITION": 473, "NORMAL": 474, "TEXCOORD_0": 475}, "indices": 472, "mode": 4, "material": 21, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 118, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}]}, {"primitives": [{"attributes": {"POSITION": 477, "NORMAL": 478, "TEXCOORD_0": 479}, "indices": 476, "mode": 4, "material": 18, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 119, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}]}, {"primitives": [{"attributes": {"POSITION": 481, "NORMAL": 482, "TEXCOORD_0": 483}, "indices": 480, "mode": 4, "material": 18, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 120, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}]}, {"primitives": [{"attributes": {"POSITION": 485, "NORMAL": 486, "TEXCOORD_0": 487}, "indices": 484, "mode": 4, "material": 18, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 121, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}]}, {"primitives": [{"attributes": {"POSITION": 489, "NORMAL": 490, "TEXCOORD_0": 491}, "indices": 488, "mode": 4, "material": 22, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 122, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}]}, {"primitives": [{"attributes": {"POSITION": 493, "NORMAL": 494, "TEXCOORD_0": 495}, "indices": 492, "mode": 4, "material": 22, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 123, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}]}, {"primitives": [{"attributes": {"POSITION": 497, "NORMAL": 498, "TEXCOORD_0": 499}, "indices": 496, "mode": 4, "material": 22, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 124, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}]}, {"primitives": [{"attributes": {"POSITION": 501, "NORMAL": 502, "TEXCOORD_0": 503}, "indices": 500, "mode": 4, "material": 37, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 125, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}]}, {"primitives": [{"attributes": {"POSITION": 505, "NORMAL": 506, "TEXCOORD_0": 507}, "indices": 504, "mode": 4, "material": 38, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 126, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}, {"attributes": {"POSITION": 509, "NORMAL": 510, "TEXCOORD_0": 511}, "indices": 508, "mode": 4, "material": 39, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 127, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}, {"attributes": {"POSITION": 513, "NORMAL": 514, "TEXCOORD_0": 515}, "indices": 512, "mode": 4, "material": 40, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 128, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}]}], "accessors": [{"componentType": 5123, "count": 768, "type": "SCALAR"}, {"componentType": 5126, "count": 256, "type": "VEC3", "min": [-0.00253771129, -0.0174624994, -0.00254000002], "max": [0.00253771129, 0.0174624994, 0.00254000002]}, {"componentType": 5126, "count": 256, "type": "VEC3"}, {"componentType": 5126, "count": 256, "type": "VEC2"}, {"componentType": 5123, "count": 768, "type": "SCALAR"}, {"componentType": 5126, "count": 256, "type": "VEC3", "min": [-0.00253771129, -0.0174624994, -0.00254000002], "max": [0.00253771129, 0.0174624994, 0.00254000002]}, {"componentType": 5126, "count": 256, "type": "VEC3"}, {"componentType": 5126, "count": 256, "type": "VEC2"}, {"componentType": 5123, "count": 10956, "type": "SCALAR"}, {"componentType": 5126, "count": 3724, "type": "VEC3", "min": [-0.0250189994, 0.0, 4.33680869e-19], "max": [0.0250189994, 0.00152399996, 0.144703805]}, {"componentType": 5126, "count": 3724, "type": "VEC3"}, {"componentType": 5126, "count": 3724, "type": "VEC2"}, {"componentType": 5123, "count": 768, "type": "SCALAR"}, {"componentType": 5126, "count": 256, "type": "VEC3", "min": [-0.00253771129, -0.0174624994, -0.00254000002], "max": [0.00253771129, 0.0174624994, 0.00254000002]}, {"componentType": 5126, "count": 256, "type": "VEC3"}, {"componentType": 5126, "count": 256, "type": "VEC2"}, {"componentType": 5123, "count": 768, "type": "SCALAR"}, {"componentType": 5126, "count": 256, "type": "VEC3", "min": [-0.00253771129, -0.0174624994, -0.00254000002], "max": [0.00253771129, 0.0174624994, 0.00254000002]}, {"componentType": 5126, "count": 256, "type": "VEC3"}, {"componentType": 5126, "count": 256, "type": "VEC2"}, {"componentType": 5123, "count": 936, "type": "SCALAR"}, {"componentType": 5126, "count": 312, "type": "VEC3", "min": [-0.00253845262, -0.00508000003, -0.00254000002], "max": [0.00253845262, 0.00508000003, 0.00254000002]}, {"componentType": 5126, "count": 312, "type": "VEC3"}, {"componentType": 5126, "count": 312, "type": "VEC2"}, {"componentType": 5123, "count": 936, "type": "SCALAR"}, {"componentType": 5126, "count": 312, "type": "VEC3", "min": [-0.00253845262, -0.00508000003, -0.00254000002], "max": [0.00253845262, 0.00508000003, 0.00254000002]}, {"componentType": 5126, "count": 312, "type": "VEC3"}, {"componentType": 5126, "count": 312, "type": "VEC2"}, {"componentType": 5123, "count": 936, "type": "SCALAR"}, {"componentType": 5126, "count": 312, "type": "VEC3", "min": [-0.00253845262, -0.00508000003, -0.00254000002], "max": [0.00253845262, 0.00508000003, 0.00254000002]}, {"componentType": 5126, "count": 312, "type": "VEC3"}, {"componentType": 5126, "count": 312, "type": "VEC2"}, {"componentType": 5123, "count": 936, "type": "SCALAR"}, {"componentType": 5126, "count": 312, "type": "VEC3", "min": [-0.00253845262, -0.00508000003, -0.00254000002], "max": [0.00253845262, 0.00508000003, 0.00254000002]}, {"componentType": 5126, "count": 312, "type": "VEC3"}, {"componentType": 5126, "count": 312, "type": "VEC2"}, {"componentType": 5123, "count": 5112, "type": "SCALAR"}, {"componentType": 5126, "count": 1642, "type": "VEC3", "min": [-0.0287941955, 0.0, 0.0529892445], "max": [0.0287941955, 0.00152399996, 0.148293048]}, {"componentType": 5126, "count": 1642, "type": "VEC3"}, {"componentType": 5126, "count": 1642, "type": "VEC2"}, {"componentType": 5123, "count": 768, "type": "SCALAR"}, {"componentType": 5126, "count": 256, "type": "VEC3", "min": [-0.00253771129, -0.0174624994, -0.00254000002], "max": [0.00253771129, 0.0174624994, 0.00254000002]}, {"componentType": 5126, "count": 256, "type": "VEC3"}, {"componentType": 5126, "count": 256, "type": "VEC2"}, {"componentType": 5123, "count": 33144, "type": "SCALAR"}, {"componentType": 5126, "count": 11434, "type": "VEC3", "min": [-0.103773691, 0.0, -0.0157734007], "max": [0.103773691, 0.00312420004, 0.204756901]}, {"componentType": 5126, "count": 11434, "type": "VEC3"}, {"componentType": 5126, "count": 11434, "type": "VEC2"}, {"componentType": 5123, "count": 768, "type": "SCALAR"}, {"componentType": 5126, "count": 256, "type": "VEC3", "min": [-0.00253771129, -0.0174624994, -0.00254000002], "max": [0.00253771129, 0.0174624994, 0.00254000002]}, {"componentType": 5126, "count": 256, "type": "VEC3"}, {"componentType": 5126, "count": 256, "type": "VEC2"}, {"componentType": 5123, "count": 1188, "type": "SCALAR"}, {"componentType": 5126, "count": 416, "type": "VEC3", "min": [-0.0102615999, 0.0104775, -0.0102615999], "max": [0.0102615999, 0.0113976756, 0.0095190322]}, {"componentType": 5126, "count": 416, "type": "VEC3"}, {"componentType": 5126, "count": 416, "type": "VEC2"}, {"componentType": 5123, "count": 1500, "type": "SCALAR"}, {"componentType": 5126, "count": 500, "type": "VEC3", "min": [-0.0115047172, 0.00508000003, -0.0115315998], "max": [0.0115047172, 0.0189484004, 0.0115315998]}, {"componentType": 5126, "count": 500, "type": "VEC3"}, {"componentType": 5126, "count": 500, "type": "VEC2"}, {"componentType": 5123, "count": 64998, "type": "SCALAR"}, {"componentType": 5126, "count": 21678, "type": "VEC3", "min": [-0.00556581514, 0.0104775, -0.00079374999], "max": [0.00158749998, 0.0136524998, 0.0102615999]}, {"componentType": 5126, "count": 21678, "type": "VEC3"}, {"componentType": 5126, "count": 21678, "type": "VEC2"}, {"componentType": 5123, "count": 64998, "type": "SCALAR"}, {"componentType": 5126, "count": 21688, "type": "VEC3", "min": [-0.00952640548, 0.0104775, -0.00158749998], "max": [0.00079374999, 0.0136524998, 0.00952640548]}, {"componentType": 5126, "count": 21688, "type": "VEC3"}, {"componentType": 5126, "count": 21688, "type": "VEC2"}, {"componentType": 5123, "count": 64998, "type": "SCALAR"}, {"componentType": 5126, "count": 21688, "type": "VEC3", "min": [-0.0102615999, 0.0104775, -0.00750284875], "max": [0.00137481536, 0.0136524998, 0.00158749998]}, {"componentType": 5126, "count": 21688, "type": "VEC3"}, {"componentType": 5126, "count": 21688, "type": "VEC2"}, {"componentType": 5123, "count": 64998, "type": "SCALAR"}, {"componentType": 5126, "count": 21686, "type": "VEC3", "min": [-0.00639131526, 0.0104775, -0.0102615999], "max": [0.00433289865, 0.0136524998, 0.00079374999]}, {"componentType": 5126, "count": 21686, "type": "VEC3"}, {"componentType": 5126, "count": 21686, "type": "VEC2"}, {"componentType": 5123, "count": 64998, "type": "SCALAR"}, {"componentType": 5126, "count": 21688, "type": "VEC3", "min": [-0.00079374999, 0.0104775, -0.00952640548], "max": [0.00952640548, 0.0136524998, 0.00158749998]}, {"componentType": 5126, "count": 21688, "type": "VEC3"}, {"componentType": 5126, "count": 21688, "type": "VEC2"}, {"componentType": 5123, "count": 64998, "type": "SCALAR"}, {"componentType": 5126, "count": 21686, "type": "VEC3", "min": [-0.00137481536, 0.0104775, -0.00158749998], "max": [0.0102615999, 0.0136524998, 0.00639131526]}, {"componentType": 5126, "count": 21686, "type": "VEC3"}, {"componentType": 5126, "count": 21686, "type": "VEC2"}, {"componentType": 5123, "count": 14976, "type": "SCALAR"}, {"componentType": 5126, "count": 5002, "type": "VEC3", "min": [0.00122868468, 0.0104775, 0.00377606484], "max": [0.00639131526, 0.0136524998, 0.00952640548]}, {"componentType": 5126, "count": 5002, "type": "VEC3"}, {"componentType": 5126, "count": 5002, "type": "VEC2"}, {"componentType": 5123, "count": 8718, "type": "SCALAR"}, {"componentType": 5126, "count": 2586, "type": "VEC3", "min": [-0.0115047172, -0.00127000001, -0.0115315998], "max": [0.0115047172, 0.0333501995, 0.0115315998]}, {"componentType": 5126, "count": 2586, "type": "VEC3"}, {"componentType": 5126, "count": 2586, "type": "VEC2"}, {"componentType": 5123, "count": 10608, "type": "SCALAR"}, {"componentType": 5126, "count": 2402, "type": "VEC3", "min": [-0.00589231215, -2.16840434e-19, -0.00590220233], "max": [0.00590550015, 0.0160019994, 0.00590220233]}, {"componentType": 5126, "count": 2402, "type": "VEC3"}, {"componentType": 5126, "count": 2402, "type": "VEC2"}, {"componentType": 5123, "count": 1188, "type": "SCALAR"}, {"componentType": 5126, "count": 416, "type": "VEC3", "min": [-0.0102615999, 0.0104775, -0.0102615999], "max": [0.0102615999, 0.0113976756, 0.0095190322]}, {"componentType": 5126, "count": 416, "type": "VEC3"}, {"componentType": 5126, "count": 416, "type": "VEC2"}, {"componentType": 5123, "count": 1500, "type": "SCALAR"}, {"componentType": 5126, "count": 500, "type": "VEC3", "min": [-0.0115047172, 0.00508000003, -0.0115315998], "max": [0.0115047172, 0.0189484004, 0.0115315998]}, {"componentType": 5126, "count": 500, "type": "VEC3"}, {"componentType": 5126, "count": 500, "type": "VEC2"}, {"componentType": 5123, "count": 64998, "type": "SCALAR"}, {"componentType": 5126, "count": 21678, "type": "VEC3", "min": [-0.00556581514, 0.0104775, -0.00079374999], "max": [0.00158749998, 0.0136524998, 0.0102615999]}, {"componentType": 5126, "count": 21678, "type": "VEC3"}, {"componentType": 5126, "count": 21678, "type": "VEC2"}, {"componentType": 5123, "count": 64998, "type": "SCALAR"}, {"componentType": 5126, "count": 21688, "type": "VEC3", "min": [-0.00952640548, 0.0104775, -0.00158749998], "max": [0.00079374999, 0.0136524998, 0.00952640548]}, {"componentType": 5126, "count": 21688, "type": "VEC3"}, {"componentType": 5126, "count": 21688, "type": "VEC2"}, {"componentType": 5123, "count": 64998, "type": "SCALAR"}, {"componentType": 5126, "count": 21688, "type": "VEC3", "min": [-0.0102615999, 0.0104775, -0.00750284875], "max": [0.00137481536, 0.0136524998, 0.00158749998]}, {"componentType": 5126, "count": 21688, "type": "VEC3"}, {"componentType": 5126, "count": 21688, "type": "VEC2"}, {"componentType": 5123, "count": 64998, "type": "SCALAR"}, {"componentType": 5126, "count": 21686, "type": "VEC3", "min": [-0.00639131526, 0.0104775, -0.0102615999], "max": [0.00433289865, 0.0136524998, 0.00079374999]}, {"componentType": 5126, "count": 21686, "type": "VEC3"}, {"componentType": 5126, "count": 21686, "type": "VEC2"}, {"componentType": 5123, "count": 64998, "type": "SCALAR"}, {"componentType": 5126, "count": 21688, "type": "VEC3", "min": [-0.00079374999, 0.0104775, -0.00952640548], "max": [0.00952640548, 0.0136524998, 0.00158749998]}, {"componentType": 5126, "count": 21688, "type": "VEC3"}, {"componentType": 5126, "count": 21688, "type": "VEC2"}, {"componentType": 5123, "count": 64998, "type": "SCALAR"}, {"componentType": 5126, "count": 21686, "type": "VEC3", "min": [-0.00137481536, 0.0104775, -0.00158749998], "max": [0.0102615999, 0.0136524998, 0.00639131526]}, {"componentType": 5126, "count": 21686, "type": "VEC3"}, {"componentType": 5126, "count": 21686, "type": "VEC2"}, {"componentType": 5123, "count": 14976, "type": "SCALAR"}, {"componentType": 5126, "count": 5002, "type": "VEC3", "min": [0.00122868468, 0.0104775, 0.00377606484], "max": [0.00639131526, 0.0136524998, 0.00952640548]}, {"componentType": 5126, "count": 5002, "type": "VEC3"}, {"componentType": 5126, "count": 5002, "type": "VEC2"}, {"componentType": 5123, "count": 8718, "type": "SCALAR"}, {"componentType": 5126, "count": 2586, "type": "VEC3", "min": [-0.0115047172, -0.00127000001, -0.0115315998], "max": [0.0115047172, 0.0333501995, 0.0115315998]}, {"componentType": 5126, "count": 2586, "type": "VEC3"}, {"componentType": 5126, "count": 2586, "type": "VEC2"}, {"componentType": 5123, "count": 10608, "type": "SCALAR"}, {"componentType": 5126, "count": 2402, "type": "VEC3", "min": [-0.00589231215, -2.16840434e-19, -0.00590220233], "max": [0.00590550015, 0.0160019994, 0.00590220233]}, {"componentType": 5126, "count": 2402, "type": "VEC3"}, {"componentType": 5126, "count": 2402, "type": "VEC2"}, {"componentType": 5123, "count": 270, "type": "SCALAR"}, {"componentType": 5126, "count": 91, "type": "VEC3", "min": [-0.112332113, 0.0110413805, -0.0142874997], "max": [1.17549435e-38, 0.0140893804, 1.17549435e-38]}, {"componentType": 5126, "count": 91, "type": "VEC3"}, {"componentType": 5126, "count": 91, "type": "VEC2"}, {"componentType": 5123, "count": 3786, "type": "SCALAR"}, {"componentType": 5126, "count": 735, "type": "VEC3", "min": [-0.112332113, 0.00317499996, -0.0142874997], "max": [1.17549435e-38, 0.0109143797, 1.17549435e-38]}, {"componentType": 5126, "count": 735, "type": "VEC3"}, {"componentType": 5126, "count": 735, "type": "VEC2"}, {"componentType": 5123, "count": 2940, "type": "SCALAR"}, {"componentType": 5126, "count": 696, "type": "VEC3", "min": [-0.112332113, -3.46944695e-18, -0.0168274995], "max": [0.0346710011, 0.0213360004, 0.0168274995]}, {"componentType": 5126, "count": 696, "type": "VEC3"}, {"componentType": 5126, "count": 696, "type": "VEC2"}, {"componentType": 5123, "count": 3828, "type": "SCALAR"}, {"componentType": 5126, "count": 1061, "type": "VEC3", "min": [-1.30104261e-18, -0.00238760002, -0.00238760002], "max": [0.0140207997, 0.00238760002, 0.00238760002]}, {"componentType": 5126, "count": 1061, "type": "VEC3"}, {"componentType": 5126, "count": 1061, "type": "VEC2"}, {"componentType": 5123, "count": 3828, "type": "SCALAR"}, {"componentType": 5126, "count": 1061, "type": "VEC3", "min": [-1.30104261e-18, -0.00238760002, -0.00238760002], "max": [0.0140207997, 0.00238760002, 0.00238760002]}, {"componentType": 5126, "count": 1061, "type": "VEC3"}, {"componentType": 5126, "count": 1061, "type": "VEC2"}, {"componentType": 5123, "count": 3912, "type": "SCALAR"}, {"componentType": 5126, "count": 1136, "type": "VEC3", "min": [0.0, -0.00177800003, -0.00177800003], "max": [0.00853439979, 0.00177800003, 0.00177800003]}, {"componentType": 5126, "count": 1136, "type": "VEC3"}, {"componentType": 5126, "count": 1136, "type": "VEC2"}, {"componentType": 5123, "count": 1188, "type": "SCALAR"}, {"componentType": 5126, "count": 416, "type": "VEC3", "min": [-0.0102615999, 0.0104775, -0.0102615999], "max": [0.0102615999, 0.0113976756, 0.0095190322]}, {"componentType": 5126, "count": 416, "type": "VEC3"}, {"componentType": 5126, "count": 416, "type": "VEC2"}, {"componentType": 5123, "count": 1500, "type": "SCALAR"}, {"componentType": 5126, "count": 500, "type": "VEC3", "min": [-0.0115047172, 0.00508000003, -0.0115315998], "max": [0.0115047172, 0.0189484004, 0.0115315998]}, {"componentType": 5126, "count": 500, "type": "VEC3"}, {"componentType": 5126, "count": 500, "type": "VEC2"}, {"componentType": 5123, "count": 64998, "type": "SCALAR"}, {"componentType": 5126, "count": 21678, "type": "VEC3", "min": [-0.00556581514, 0.0104775, -0.00079374999], "max": [0.00158749998, 0.0136524998, 0.0102615999]}, {"componentType": 5126, "count": 21678, "type": "VEC3"}, {"componentType": 5126, "count": 21678, "type": "VEC2"}, {"componentType": 5123, "count": 64998, "type": "SCALAR"}, {"componentType": 5126, "count": 21688, "type": "VEC3", "min": [-0.00952640548, 0.0104775, -0.00158749998], "max": [0.00079374999, 0.0136524998, 0.00952640548]}, {"componentType": 5126, "count": 21688, "type": "VEC3"}, {"componentType": 5126, "count": 21688, "type": "VEC2"}, {"componentType": 5123, "count": 64998, "type": "SCALAR"}, {"componentType": 5126, "count": 21688, "type": "VEC3", "min": [-0.0102615999, 0.0104775, -0.00750284875], "max": [0.00137481536, 0.0136524998, 0.00158749998]}, {"componentType": 5126, "count": 21688, "type": "VEC3"}, {"componentType": 5126, "count": 21688, "type": "VEC2"}, {"componentType": 5123, "count": 64998, "type": "SCALAR"}, {"componentType": 5126, "count": 21686, "type": "VEC3", "min": [-0.00639131526, 0.0104775, -0.0102615999], "max": [0.00433289865, 0.0136524998, 0.00079374999]}, {"componentType": 5126, "count": 21686, "type": "VEC3"}, {"componentType": 5126, "count": 21686, "type": "VEC2"}, {"componentType": 5123, "count": 64998, "type": "SCALAR"}, {"componentType": 5126, "count": 21688, "type": "VEC3", "min": [-0.00079374999, 0.0104775, -0.00952640548], "max": [0.00952640548, 0.0136524998, 0.00158749998]}, {"componentType": 5126, "count": 21688, "type": "VEC3"}, {"componentType": 5126, "count": 21688, "type": "VEC2"}, {"componentType": 5123, "count": 64998, "type": "SCALAR"}, {"componentType": 5126, "count": 21686, "type": "VEC3", "min": [-0.00137481536, 0.0104775, -0.00158749998], "max": [0.0102615999, 0.0136524998, 0.00639131526]}, {"componentType": 5126, "count": 21686, "type": "VEC3"}, {"componentType": 5126, "count": 21686, "type": "VEC2"}, {"componentType": 5123, "count": 14976, "type": "SCALAR"}, {"componentType": 5126, "count": 5002, "type": "VEC3", "min": [0.00122868468, 0.0104775, 0.00377606484], "max": [0.00639131526, 0.0136524998, 0.00952640548]}, {"componentType": 5126, "count": 5002, "type": "VEC3"}, {"componentType": 5126, "count": 5002, "type": "VEC2"}, {"componentType": 5123, "count": 8718, "type": "SCALAR"}, {"componentType": 5126, "count": 2586, "type": "VEC3", "min": [-0.0115047172, -0.00127000001, -0.0115315998], "max": [0.0115047172, 0.0333501995, 0.0115315998]}, {"componentType": 5126, "count": 2586, "type": "VEC3"}, {"componentType": 5126, "count": 2586, "type": "VEC2"}, {"componentType": 5123, "count": 3912, "type": "SCALAR"}, {"componentType": 5126, "count": 1136, "type": "VEC3", "min": [0.0, -0.00177800003, -0.00177800003], "max": [0.00853439979, 0.00177800003, 0.00177800003]}, {"componentType": 5126, "count": 1136, "type": "VEC3"}, {"componentType": 5126, "count": 1136, "type": "VEC2"}, {"componentType": 5123, "count": 4980, "type": "SCALAR"}, {"componentType": 5126, "count": 1280, "type": "VEC3", "min": [3.32155068e-05, -0.0024493013, -0.00245109992], "max": [0.0176784005, 0.0024493013, 0.00245109992]}, {"componentType": 5126, "count": 1280, "type": "VEC3"}, {"componentType": 5126, "count": 1280, "type": "VEC2"}, {"componentType": 5123, "count": 432, "type": "SCALAR"}, {"componentType": 5126, "count": 156, "type": "VEC3", "min": [-0.00329955667, -0.00508000003, -0.00285749999], "max": [0.00329955667, 0.00508000003, 0.00285749999]}, {"componentType": 5126, "count": 156, "type": "VEC3"}, {"componentType": 5126, "count": 156, "type": "VEC2"}, {"componentType": 5123, "count": 3072, "type": "SCALAR"}, {"componentType": 5126, "count": 1060, "type": "VEC3", "min": [-0.00238760002, -0.00275696302, -0.00167639996], "max": [0.00238760002, 0.00275696302, 3.46944695e-18]}, {"componentType": 5126, "count": 1060, "type": "VEC3"}, {"componentType": 5126, "count": 1060, "type": "VEC2"}, {"componentType": 5123, "count": 5376, "type": "SCALAR"}, {"componentType": 5126, "count": 1716, "type": "VEC3", "min": [-0.0636562183, 0.0, -0.00866100658], "max": [0.0636562183, 0.00612139981, 0.00866100658]}, {"componentType": 5126, "count": 1716, "type": "VEC3"}, {"componentType": 5126, "count": 1716, "type": "VEC2"}, {"componentType": 5123, "count": 432, "type": "SCALAR"}, {"componentType": 5126, "count": 156, "type": "VEC3", "min": [-0.00329955667, -0.00508000003, -0.00285749999], "max": [0.00329955667, 0.00508000003, 0.00285749999]}, {"componentType": 5126, "count": 156, "type": "VEC3"}, {"componentType": 5126, "count": 156, "type": "VEC2"}, {"componentType": 5123, "count": 432, "type": "SCALAR"}, {"componentType": 5126, "count": 156, "type": "VEC3", "min": [-0.00329955667, -0.00508000003, -0.00285749999], "max": [0.00329955667, 0.00508000003, 0.00285749999]}, {"componentType": 5126, "count": 156, "type": "VEC3"}, {"componentType": 5126, "count": 156, "type": "VEC2"}, {"componentType": 5123, "count": 600, "type": "SCALAR"}, {"componentType": 5126, "count": 200, "type": "VEC3", "min": [-0.00126749394, 0.0, -0.00127000001], "max": [0.00126749394, 0.101599999, 0.00127000001]}, {"componentType": 5126, "count": 200, "type": "VEC3"}, {"componentType": 5126, "count": 200, "type": "VEC2"}, {"componentType": 5123, "count": 1296, "type": "SCALAR"}, {"componentType": 5126, "count": 268, "type": "VEC3", "min": [0.0209803991, 0.00353060011, -0.00205740007], "max": [0.028634008, 0.0234551448, 0.00205740007]}, {"componentType": 5126, "count": 268, "type": "VEC3"}, {"componentType": 5126, "count": 268, "type": "VEC2"}, {"componentType": 5123, "count": 11646, "type": "SCALAR"}, {"componentType": 5126, "count": 2526, "type": "VEC3", "min": [-0.0195834003, -2.16840434e-19, -0.0160363168], "max": [0.0444153771, 0.0487219766, 0.0160363168]}, {"componentType": 5126, "count": 2526, "type": "VEC3"}, {"componentType": 5126, "count": 2526, "type": "VEC2"}, {"componentType": 5123, "count": 3036, "type": "SCALAR"}, {"componentType": 5126, "count": 684, "type": "VEC3", "min": [-0.0301751997, -0.127814561, -0.054164432], "max": [1.17549435e-38, 0.0053340001, 0.054164432]}, {"componentType": 5126, "count": 684, "type": "VEC3"}, {"componentType": 5126, "count": 684, "type": "VEC2"}, {"componentType": 5123, "count": 576, "type": "SCALAR"}, {"componentType": 5126, "count": 192, "type": "VEC3", "min": [-0.0301751997, -0.127814561, -0.054164432], "max": [1.17549435e-38, 1.17549435e-38, 0.054164432]}, {"componentType": 5126, "count": 192, "type": "VEC3"}, {"componentType": 5126, "count": 192, "type": "VEC2"}, {"componentType": 5123, "count": 3972, "type": "SCALAR"}, {"componentType": 5126, "count": 1407, "type": "VEC3", "min": [-0.0207137, 0.0, -0.0138429999], "max": [0.0207137, 0.0124460002, 0.0138429999]}, {"componentType": 5126, "count": 1407, "type": "VEC3"}, {"componentType": 5126, "count": 1407, "type": "VEC2"}, {"componentType": 5123, "count": 3828, "type": "SCALAR"}, {"componentType": 5126, "count": 1061, "type": "VEC3", "min": [-1.30104261e-18, -0.00238760002, -0.00238760002], "max": [0.0140207997, 0.00238760002, 0.00238760002]}, {"componentType": 5126, "count": 1061, "type": "VEC3"}, {"componentType": 5126, "count": 1061, "type": "VEC2"}, {"componentType": 5123, "count": 432, "type": "SCALAR"}, {"componentType": 5126, "count": 156, "type": "VEC3", "min": [-0.00329955667, -0.00508000003, -0.00285749999], "max": [0.00329955667, 0.00508000003, 0.00285749999]}, {"componentType": 5126, "count": 156, "type": "VEC3"}, {"componentType": 5126, "count": 156, "type": "VEC2"}, {"componentType": 5123, "count": 4452, "type": "SCALAR"}, {"componentType": 5126, "count": 924, "type": "VEC3", "min": [-0.0128777996, -0.0121919997, 0.0], "max": [0.0128777996, 0.0121919997, 0.0106680002]}, {"componentType": 5126, "count": 924, "type": "VEC3"}, {"componentType": 5126, "count": 924, "type": "VEC2"}, {"componentType": 5123, "count": 3912, "type": "SCALAR"}, {"componentType": 5126, "count": 1136, "type": "VEC3", "min": [0.0, -0.00177800003, -0.00177800003], "max": [0.00853439979, 0.00177800003, 0.00177800003]}, {"componentType": 5126, "count": 1136, "type": "VEC3"}, {"componentType": 5126, "count": 1136, "type": "VEC2"}, {"componentType": 5123, "count": 4176, "type": "SCALAR"}, {"componentType": 5126, "count": 1968, "type": "VEC3", "min": [-0.00948077347, 0.001651, -0.0266699996], "max": [0.00385422679, 0.00831849966, 1.17549435e-38]}, {"componentType": 5126, "count": 1968, "type": "VEC3"}, {"componentType": 5126, "count": 1968, "type": "VEC2"}, {"componentType": 5123, "count": 12132, "type": "SCALAR"}, {"componentType": 5126, "count": 4644, "type": "VEC3", "min": [0.00480672671, -0.00395999989, -0.0194310006], "max": [0.0187960006, 0.00914399978, 0.00500026019]}, {"componentType": 5126, "count": 4644, "type": "VEC3"}, {"componentType": 5126, "count": 4644, "type": "VEC2"}, {"componentType": 5123, "count": 177, "type": "SCALAR"}, {"componentType": 5126, "count": 113, "type": "VEC3", "min": [-0.00508000003, 0.001651, -0.00952500012], "max": [0.0181610007, 0.00482599996, 0.0181610007]}, {"componentType": 5126, "count": 113, "type": "VEC3"}, {"componentType": 5126, "count": 113, "type": "VEC2"}, {"componentType": 5123, "count": 1092, "type": "SCALAR"}, {"componentType": 5126, "count": 266, "type": "VEC3", "min": [-0.0104332734, 0.00177800003, -0.0194310006], "max": [0.00480672671, 0.00914399978, 1.17549435e-38]}, {"componentType": 5126, "count": 266, "type": "VEC3"}, {"componentType": 5126, "count": 266, "type": "VEC2"}, {"componentType": 5123, "count": 2445, "type": "SCALAR"}, {"componentType": 5126, "count": 740, "type": "VEC3", "min": [-0.0181610007, 0.0, -0.0181610007], "max": [0.0181610007, 0.00482599996, 0.0181610007]}, {"componentType": 5126, "count": 740, "type": "VEC3"}, {"componentType": 5126, "count": 740, "type": "VEC2"}, {"componentType": 5123, "count": 10608, "type": "SCALAR"}, {"componentType": 5126, "count": 2402, "type": "VEC3", "min": [-0.00589231215, -2.16840434e-19, -0.00590220233], "max": [0.00590550015, 0.0160019994, 0.00590220233]}, {"componentType": 5126, "count": 2402, "type": "VEC3"}, {"componentType": 5126, "count": 2402, "type": "VEC2"}, {"componentType": 5123, "count": 5160, "type": "SCALAR"}, {"componentType": 5126, "count": 1174, "type": "VEC3", "min": [-0.0139699997, -1.62630326e-19, -0.00761999981], "max": [0.0139699997, 0.00698499987, 0.00761999981]}, {"componentType": 5126, "count": 1174, "type": "VEC3"}, {"componentType": 5126, "count": 1174, "type": "VEC2"}, {"componentType": 5123, "count": 6540, "type": "SCALAR"}, {"componentType": 5126, "count": 1404, "type": "VEC3", "min": [0.0131044947, 0.0013886427, -0.00558800017], "max": [0.0185384583, 0.00646635471, 0.00558800017]}, {"componentType": 5126, "count": 1404, "type": "VEC3"}, {"componentType": 5126, "count": 1404, "type": "VEC2"}, {"componentType": 5123, "count": 3912, "type": "SCALAR"}, {"componentType": 5126, "count": 1136, "type": "VEC3", "min": [0.0, -0.00177800003, -0.00177800003], "max": [0.00853439979, 0.00177800003, 0.00177800003]}, {"componentType": 5126, "count": 1136, "type": "VEC3"}, {"componentType": 5126, "count": 1136, "type": "VEC2"}, {"componentType": 5123, "count": 3828, "type": "SCALAR"}, {"componentType": 5126, "count": 1061, "type": "VEC3", "min": [-1.30104261e-18, -0.00238760002, -0.00238760002], "max": [0.0140207997, 0.00238760002, 0.00238760002]}, {"componentType": 5126, "count": 1061, "type": "VEC3"}, {"componentType": 5126, "count": 1061, "type": "VEC2"}, {"componentType": 5123, "count": 3828, "type": "SCALAR"}, {"componentType": 5126, "count": 1061, "type": "VEC3", "min": [-1.30104261e-18, -0.00238760002, -0.00238760002], "max": [0.0140207997, 0.00238760002, 0.00238760002]}, {"componentType": 5126, "count": 1061, "type": "VEC3"}, {"componentType": 5126, "count": 1061, "type": "VEC2"}, {"componentType": 5123, "count": 3828, "type": "SCALAR"}, {"componentType": 5126, "count": 1061, "type": "VEC3", "min": [-1.30104261e-18, -0.00238760002, -0.00238760002], "max": [0.0140207997, 0.00238760002, 0.00238760002]}, {"componentType": 5126, "count": 1061, "type": "VEC3"}, {"componentType": 5126, "count": 1061, "type": "VEC2"}, {"componentType": 5123, "count": 3828, "type": "SCALAR"}, {"componentType": 5126, "count": 1061, "type": "VEC3", "min": [-1.30104261e-18, -0.00238760002, -0.00238760002], "max": [0.0140207997, 0.00238760002, 0.00238760002]}, {"componentType": 5126, "count": 1061, "type": "VEC3"}, {"componentType": 5126, "count": 1061, "type": "VEC2"}, {"componentType": 5123, "count": 1188, "type": "SCALAR"}, {"componentType": 5126, "count": 416, "type": "VEC3", "min": [-0.0102615999, 0.0104775, -0.0102615999], "max": [0.0102615999, 0.0113976756, 0.0095190322]}, {"componentType": 5126, "count": 416, "type": "VEC3"}, {"componentType": 5126, "count": 416, "type": "VEC2"}, {"componentType": 5123, "count": 1500, "type": "SCALAR"}, {"componentType": 5126, "count": 500, "type": "VEC3", "min": [-0.0115047172, 0.00508000003, -0.0115315998], "max": [0.0115047172, 0.0189484004, 0.0115315998]}, {"componentType": 5126, "count": 500, "type": "VEC3"}, {"componentType": 5126, "count": 500, "type": "VEC2"}, {"componentType": 5123, "count": 64998, "type": "SCALAR"}, {"componentType": 5126, "count": 21678, "type": "VEC3", "min": [-0.00556581514, 0.0104775, -0.00079374999], "max": [0.00158749998, 0.0136524998, 0.0102615999]}, {"componentType": 5126, "count": 21678, "type": "VEC3"}, {"componentType": 5126, "count": 21678, "type": "VEC2"}, {"componentType": 5123, "count": 64998, "type": "SCALAR"}, {"componentType": 5126, "count": 21688, "type": "VEC3", "min": [-0.00952640548, 0.0104775, -0.00158749998], "max": [0.00079374999, 0.0136524998, 0.00952640548]}, {"componentType": 5126, "count": 21688, "type": "VEC3"}, {"componentType": 5126, "count": 21688, "type": "VEC2"}, {"componentType": 5123, "count": 64998, "type": "SCALAR"}, {"componentType": 5126, "count": 21688, "type": "VEC3", "min": [-0.0102615999, 0.0104775, -0.00750284875], "max": [0.00137481536, 0.0136524998, 0.00158749998]}, {"componentType": 5126, "count": 21688, "type": "VEC3"}, {"componentType": 5126, "count": 21688, "type": "VEC2"}, {"componentType": 5123, "count": 64998, "type": "SCALAR"}, {"componentType": 5126, "count": 21686, "type": "VEC3", "min": [-0.00639131526, 0.0104775, -0.0102615999], "max": [0.00433289865, 0.0136524998, 0.00079374999]}, {"componentType": 5126, "count": 21686, "type": "VEC3"}, {"componentType": 5126, "count": 21686, "type": "VEC2"}, {"componentType": 5123, "count": 64998, "type": "SCALAR"}, {"componentType": 5126, "count": 21688, "type": "VEC3", "min": [-0.00079374999, 0.0104775, -0.00952640548], "max": [0.00952640548, 0.0136524998, 0.00158749998]}, {"componentType": 5126, "count": 21688, "type": "VEC3"}, {"componentType": 5126, "count": 21688, "type": "VEC2"}, {"componentType": 5123, "count": 64998, "type": "SCALAR"}, {"componentType": 5126, "count": 21686, "type": "VEC3", "min": [-0.00137481536, 0.0104775, -0.00158749998], "max": [0.0102615999, 0.0136524998, 0.00639131526]}, {"componentType": 5126, "count": 21686, "type": "VEC3"}, {"componentType": 5126, "count": 21686, "type": "VEC2"}, {"componentType": 5123, "count": 14976, "type": "SCALAR"}, {"componentType": 5126, "count": 5002, "type": "VEC3", "min": [0.00122868468, 0.0104775, 0.00377606484], "max": [0.00639131526, 0.0136524998, 0.00952640548]}, {"componentType": 5126, "count": 5002, "type": "VEC3"}, {"componentType": 5126, "count": 5002, "type": "VEC2"}, {"componentType": 5123, "count": 8718, "type": "SCALAR"}, {"componentType": 5126, "count": 2586, "type": "VEC3", "min": [-0.0115047172, -0.00127000001, -0.0115315998], "max": [0.0115047172, 0.0333501995, 0.0115315998]}, {"componentType": 5126, "count": 2586, "type": "VEC3"}, {"componentType": 5126, "count": 2586, "type": "VEC2"}, {"componentType": 5123, "count": 5160, "type": "SCALAR"}, {"componentType": 5126, "count": 1174, "type": "VEC3", "min": [-0.0139699997, -1.62630326e-19, -0.00761999981], "max": [0.0139699997, 0.00698499987, 0.00761999981]}, {"componentType": 5126, "count": 1174, "type": "VEC3"}, {"componentType": 5126, "count": 1174, "type": "VEC2"}, {"componentType": 5123, "count": 6540, "type": "SCALAR"}, {"componentType": 5126, "count": 1404, "type": "VEC3", "min": [0.0131044947, 0.0013886427, -0.00558800017], "max": [0.0185384583, 0.00646635471, 0.00558800017]}, {"componentType": 5126, "count": 1404, "type": "VEC3"}, {"componentType": 5126, "count": 1404, "type": "VEC2"}, {"componentType": 5123, "count": 5160, "type": "SCALAR"}, {"componentType": 5126, "count": 1174, "type": "VEC3", "min": [-0.0139699997, -1.62630326e-19, -0.00761999981], "max": [0.0139699997, 0.00698499987, 0.00761999981]}, {"componentType": 5126, "count": 1174, "type": "VEC3"}, {"componentType": 5126, "count": 1174, "type": "VEC2"}, {"componentType": 5123, "count": 6540, "type": "SCALAR"}, {"componentType": 5126, "count": 1404, "type": "VEC3", "min": [0.0131044947, 0.0013886427, -0.00558800017], "max": [0.0185384583, 0.00646635471, 0.00558800017]}, {"componentType": 5126, "count": 1404, "type": "VEC3"}, {"componentType": 5126, "count": 1404, "type": "VEC2"}, {"componentType": 5123, "count": 5160, "type": "SCALAR"}, {"componentType": 5126, "count": 1174, "type": "VEC3", "min": [-0.0139699997, -1.62630326e-19, -0.00761999981], "max": [0.0139699997, 0.00698499987, 0.00761999981]}, {"componentType": 5126, "count": 1174, "type": "VEC3"}, {"componentType": 5126, "count": 1174, "type": "VEC2"}, {"componentType": 5123, "count": 6540, "type": "SCALAR"}, {"componentType": 5126, "count": 1404, "type": "VEC3", "min": [0.0131044947, 0.0013886427, -0.00558800017], "max": [0.0185384583, 0.00646635471, 0.00558800017]}, {"componentType": 5126, "count": 1404, "type": "VEC3"}, {"componentType": 5126, "count": 1404, "type": "VEC2"}, {"componentType": 5123, "count": 10608, "type": "SCALAR"}, {"componentType": 5126, "count": 2402, "type": "VEC3", "min": [-0.00589231215, -2.16840434e-19, -0.00590220233], "max": [0.00590550015, 0.0160019994, 0.00590220233]}, {"componentType": 5126, "count": 2402, "type": "VEC3"}, {"componentType": 5126, "count": 2402, "type": "VEC2"}, {"componentType": 5123, "count": 600, "type": "SCALAR"}, {"componentType": 5126, "count": 200, "type": "VEC3", "min": [-0.00126749394, 0.0, -0.00127000001], "max": [0.00126749394, 0.101599999, 0.00127000001]}, {"componentType": 5126, "count": 200, "type": "VEC3"}, {"componentType": 5126, "count": 200, "type": "VEC2"}, {"componentType": 5123, "count": 3828, "type": "SCALAR"}, {"componentType": 5126, "count": 1061, "type": "VEC3", "min": [-1.30104261e-18, -0.00238760002, -0.00238760002], "max": [0.0140207997, 0.00238760002, 0.00238760002]}, {"componentType": 5126, "count": 1061, "type": "VEC3"}, {"componentType": 5126, "count": 1061, "type": "VEC2"}, {"componentType": 5123, "count": 3912, "type": "SCALAR"}, {"componentType": 5126, "count": 1136, "type": "VEC3", "min": [0.0, -0.00177800003, -0.00177800003], "max": [0.00853439979, 0.00177800003, 0.00177800003]}, {"componentType": 5126, "count": 1136, "type": "VEC3"}, {"componentType": 5126, "count": 1136, "type": "VEC2"}, {"componentType": 5123, "count": 3828, "type": "SCALAR"}, {"componentType": 5126, "count": 1061, "type": "VEC3", "min": [-1.30104261e-18, -0.00238760002, -0.00238760002], "max": [0.0140207997, 0.00238760002, 0.00238760002]}, {"componentType": 5126, "count": 1061, "type": "VEC3"}, {"componentType": 5126, "count": 1061, "type": "VEC2"}, {"componentType": 5123, "count": 3828, "type": "SCALAR"}, {"componentType": 5126, "count": 1061, "type": "VEC3", "min": [-1.30104261e-18, -0.00238760002, -0.00238760002], "max": [0.0140207997, 0.00238760002, 0.00238760002]}, {"componentType": 5126, "count": 1061, "type": "VEC3"}, {"componentType": 5126, "count": 1061, "type": "VEC2"}, {"componentType": 5123, "count": 3912, "type": "SCALAR"}, {"componentType": 5126, "count": 1136, "type": "VEC3", "min": [0.0, -0.00177800003, -0.00177800003], "max": [0.00853439979, 0.00177800003, 0.00177800003]}, {"componentType": 5126, "count": 1136, "type": "VEC3"}, {"componentType": 5126, "count": 1136, "type": "VEC2"}, {"componentType": 5123, "count": 3828, "type": "SCALAR"}, {"componentType": 5126, "count": 1061, "type": "VEC3", "min": [-1.30104261e-18, -0.00238760002, -0.00238760002], "max": [0.0140207997, 0.00238760002, 0.00238760002]}, {"componentType": 5126, "count": 1061, "type": "VEC3"}, {"componentType": 5126, "count": 1061, "type": "VEC2"}, {"componentType": 5123, "count": 3828, "type": "SCALAR"}, {"componentType": 5126, "count": 1061, "type": "VEC3", "min": [-1.30104261e-18, -0.00238760002, -0.00238760002], "max": [0.0140207997, 0.00238760002, 0.00238760002]}, {"componentType": 5126, "count": 1061, "type": "VEC3"}, {"componentType": 5126, "count": 1061, "type": "VEC2"}, {"componentType": 5123, "count": 3912, "type": "SCALAR"}, {"componentType": 5126, "count": 1136, "type": "VEC3", "min": [0.0, -0.00177800003, -0.00177800003], "max": [0.00853439979, 0.00177800003, 0.00177800003]}, {"componentType": 5126, "count": 1136, "type": "VEC3"}, {"componentType": 5126, "count": 1136, "type": "VEC2"}, {"componentType": 5123, "count": 3912, "type": "SCALAR"}, {"componentType": 5126, "count": 1136, "type": "VEC3", "min": [0.0, -0.00177800003, -0.00177800003], "max": [0.00853439979, 0.00177800003, 0.00177800003]}, {"componentType": 5126, "count": 1136, "type": "VEC3"}, {"componentType": 5126, "count": 1136, "type": "VEC2"}, {"componentType": 5123, "count": 3912, "type": "SCALAR"}, {"componentType": 5126, "count": 1136, "type": "VEC3", "min": [0.0, -0.00177800003, -0.00177800003], "max": [0.00853439979, 0.00177800003, 0.00177800003]}, {"componentType": 5126, "count": 1136, "type": "VEC3"}, {"componentType": 5126, "count": 1136, "type": "VEC2"}, {"componentType": 5123, "count": 3912, "type": "SCALAR"}, {"componentType": 5126, "count": 1136, "type": "VEC3", "min": [0.0, -0.00177800003, -0.00177800003], "max": [0.00853439979, 0.00177800003, 0.00177800003]}, {"componentType": 5126, "count": 1136, "type": "VEC3"}, {"componentType": 5126, "count": 1136, "type": "VEC2"}, {"componentType": 5123, "count": 3912, "type": "SCALAR"}, {"componentType": 5126, "count": 1136, "type": "VEC3", "min": [0.0, -0.00177800003, -0.00177800003], "max": [0.00853439979, 0.00177800003, 0.00177800003]}, {"componentType": 5126, "count": 1136, "type": "VEC3"}, {"componentType": 5126, "count": 1136, "type": "VEC2"}, {"componentType": 5123, "count": 3912, "type": "SCALAR"}, {"componentType": 5126, "count": 1136, "type": "VEC3", "min": [0.0, -0.00177800003, -0.00177800003], "max": [0.00853439979, 0.00177800003, 0.00177800003]}, {"componentType": 5126, "count": 1136, "type": "VEC3"}, {"componentType": 5126, "count": 1136, "type": "VEC2"}, {"componentType": 5123, "count": 3912, "type": "SCALAR"}, {"componentType": 5126, "count": 1136, "type": "VEC3", "min": [0.0, -0.00177800003, -0.00177800003], "max": [0.00853439979, 0.00177800003, 0.00177800003]}, {"componentType": 5126, "count": 1136, "type": "VEC3"}, {"componentType": 5126, "count": 1136, "type": "VEC2"}, {"componentType": 5123, "count": 4980, "type": "SCALAR"}, {"componentType": 5126, "count": 1280, "type": "VEC3", "min": [3.32155068e-05, -0.0024493013, -0.00245109992], "max": [0.0176784005, 0.0024493013, 0.00245109992]}, {"componentType": 5126, "count": 1280, "type": "VEC3"}, {"componentType": 5126, "count": 1280, "type": "VEC2"}, {"componentType": 5123, "count": 3072, "type": "SCALAR"}, {"componentType": 5126, "count": 1060, "type": "VEC3", "min": [-0.00238760002, -0.00275696302, -0.00167639996], "max": [0.00238760002, 0.00275696302, 3.46944695e-18]}, {"componentType": 5126, "count": 1060, "type": "VEC3"}, {"componentType": 5126, "count": 1060, "type": "VEC2"}, {"componentType": 5123, "count": 4980, "type": "SCALAR"}, {"componentType": 5126, "count": 1280, "type": "VEC3", "min": [3.32155068e-05, -0.0024493013, -0.00245109992], "max": [0.0176784005, 0.0024493013, 0.00245109992]}, {"componentType": 5126, "count": 1280, "type": "VEC3"}, {"componentType": 5126, "count": 1280, "type": "VEC2"}, {"componentType": 5123, "count": 4980, "type": "SCALAR"}, {"componentType": 5126, "count": 1280, "type": "VEC3", "min": [3.32155068e-05, -0.0024493013, -0.00245109992], "max": [0.0176784005, 0.0024493013, 0.00245109992]}, {"componentType": 5126, "count": 1280, "type": "VEC3"}, {"componentType": 5126, "count": 1280, "type": "VEC2"}, {"componentType": 5123, "count": 3072, "type": "SCALAR"}, {"componentType": 5126, "count": 1060, "type": "VEC3", "min": [-0.00238760002, -0.00275696302, -0.00167639996], "max": [0.00238760002, 0.00275696302, 3.46944695e-18]}, {"componentType": 5126, "count": 1060, "type": "VEC3"}, {"componentType": 5126, "count": 1060, "type": "VEC2"}, {"componentType": 5123, "count": 3072, "type": "SCALAR"}, {"componentType": 5126, "count": 1060, "type": "VEC3", "min": [-0.00238760002, -0.00275696302, -0.00167639996], "max": [0.00238760002, 0.00275696302, 3.46944695e-18]}, {"componentType": 5126, "count": 1060, "type": "VEC3"}, {"componentType": 5126, "count": 1060, "type": "VEC2"}, {"componentType": 5123, "count": 3912, "type": "SCALAR"}, {"componentType": 5126, "count": 1136, "type": "VEC3", "min": [0.0, -0.00177800003, -0.00177800003], "max": [0.00853439979, 0.00177800003, 0.00177800003]}, {"componentType": 5126, "count": 1136, "type": "VEC3"}, {"componentType": 5126, "count": 1136, "type": "VEC2"}, {"componentType": 5123, "count": 3912, "type": "SCALAR"}, {"componentType": 5126, "count": 1136, "type": "VEC3", "min": [0.0, -0.00177800003, -0.00177800003], "max": [0.00853439979, 0.00177800003, 0.00177800003]}, {"componentType": 5126, "count": 1136, "type": "VEC3"}, {"componentType": 5126, "count": 1136, "type": "VEC2"}, {"componentType": 5123, "count": 3912, "type": "SCALAR"}, {"componentType": 5126, "count": 1136, "type": "VEC3", "min": [0.0, -0.00177800003, -0.00177800003], "max": [0.00853439979, 0.00177800003, 0.00177800003]}, {"componentType": 5126, "count": 1136, "type": "VEC3"}, {"componentType": 5126, "count": 1136, "type": "VEC2"}, {"componentType": 5123, "count": 5376, "type": "SCALAR"}, {"componentType": 5126, "count": 1716, "type": "VEC3", "min": [-0.0636562183, 0.0, -0.00866100658], "max": [0.0636562183, 0.00612139981, 0.00866100658]}, {"componentType": 5126, "count": 1716, "type": "VEC3"}, {"componentType": 5126, "count": 1716, "type": "VEC2"}, {"componentType": 5123, "count": 5376, "type": "SCALAR"}, {"componentType": 5126, "count": 1716, "type": "VEC3", "min": [-0.0636562183, 0.0, -0.00866100658], "max": [0.0636562183, 0.00612139981, 0.00866100658]}, {"componentType": 5126, "count": 1716, "type": "VEC3"}, {"componentType": 5126, "count": 1716, "type": "VEC2"}, {"componentType": 5123, "count": 5376, "type": "SCALAR"}, {"componentType": 5126, "count": 1716, "type": "VEC3", "min": [-0.0636562183, 0.0, -0.00866100658], "max": [0.0636562183, 0.00612139981, 0.00866100658]}, {"componentType": 5126, "count": 1716, "type": "VEC3"}, {"componentType": 5126, "count": 1716, "type": "VEC2"}, {"componentType": 5123, "count": 10230, "type": "SCALAR"}, {"componentType": 5126, "count": 2673, "type": "VEC3", "min": [-0.0231139995, 0.00312420004, 2.30026326e-05], "max": [0.0231139995, 0.0380491987, 0.02232025]}, {"componentType": 5126, "count": 2673, "type": "VEC3"}, {"componentType": 5126, "count": 2673, "type": "VEC2"}, {"componentType": 5123, "count": 5109, "type": "SCALAR"}, {"componentType": 5126, "count": 2127, "type": "VEC3", "min": [-0.00798634626, -0.00508000003, -0.0102235004], "max": [0.00773723098, 0.00389141031, 0.00945661496]}, {"componentType": 5126, "count": 2127, "type": "VEC3"}, {"componentType": 5126, "count": 2127, "type": "VEC2"}, {"componentType": 5123, "count": 99, "type": "SCALAR"}, {"componentType": 5126, "count": 34, "type": "VEC3", "min": [-0.00463024946, 0.0210565999, -0.00461451011], "max": [0.00463024946, 0.0210565999, 0.00463549979]}, {"componentType": 5126, "count": 34, "type": "VEC3"}, {"componentType": 5126, "count": 34, "type": "VEC2"}, {"componentType": 5123, "count": 9726, "type": "SCALAR"}, {"componentType": 5126, "count": 2846, "type": "VEC3", "min": [-0.0130174998, -0.00698499987, -0.0102235004], "max": [0.0130174998, 0.0223265998, 0.0102235004]}, {"componentType": 5126, "count": 2846, "type": "VEC3"}, {"componentType": 5126, "count": 2846, "type": "VEC2"}], "materials": [{"pbrMetallicRoughness": {"baseColorFactor": [0.498039, 0.498039, 0.498039, 1], "metallicFactor": 0, "roughnessFactor": 1}}, {"name": "test", "pbrMetallicRoughness": {"baseColorFactor": [1, 1, 1, 1], "metallicFactor": 0, "roughnessFactor": 1}}, {"name": "test-1", "pbrMetallicRoughness": {"baseColorFactor": [1, 1, 1, 1], "metallicFactor": 0, "roughnessFactor": 1}}, {"name": "test-2", "pbrMetallicRoughness": {"baseColorFactor": [1, 1, 1, 1], "metallicFactor": 0, "roughnessFactor": 1}}, {"name": "satin finish aluminum-1", "normalTexture": {"index": 0, "scale": 0.3}, "pbrMetallicRoughness": {"baseColorFactor": [0.866667, 0.909804, 1, 1], "metallicFactor": 1, "roughnessFactor": 0.1}}, {"name": "carbon fiber eproxy 2d", "normalTexture": {"index": 2, "scale": 0.3}, "pbrMetallicRoughness": {"baseColorTexture": {"index": 1}, "baseColorFactor": [1, 1, 1, 1], "metallicFactor": 0, "roughnessFactor": 0.19}}, {"name": "satin finish aluminum-2", "normalTexture": {"index": 0, "scale": 0.3}, "pbrMetallicRoughness": {"baseColorFactor": [0.866667, 0.909804, 1, 1], "metallicFactor": 1, "roughnessFactor": 0.1}}, {"name": "light grey high gloss plastic-7", "pbrMetallicRoughness": {"baseColorFactor": [1, 1, 1, 1], "metallicFactor": 0, "roughnessFactor": 0.17}}, {"name": "carbon fiber eproxy 2d-1", "normalTexture": {"index": 3, "scale": 0.3}, "pbrMetallicRoughness": {"baseColorTexture": {"index": 1}, "baseColorFactor": [1, 1, 1, 1], "metallicFactor": 0, "roughnessFactor": 0.19}}, {"name": "polished copper", "pbrMetallicRoughness": {"baseColorFactor": [0.839216, 0.466667, 0.364706, 1], "metallicFactor": 1, "roughnessFactor": 0.07}}, {"name": "color-16", "pbrMetallicRoughness": {"baseColorFactor": [0.25098, 0.25098, 0.25098, 1], "metallicFactor": 0, "roughnessFactor": 0.17}}, {"name": "color-15", "pbrMetallicRoughness": {"baseColorFactor": [0.909804, 0.443137, 0.0313725, 1], "metallicFactor": 0, "roughnessFactor": 0.17}}, {"name": "carbon steel", "pbrMetallicRoughness": {"baseColorFactor": [0.776471, 0.756863, 0.737255, 1], "metallicFactor": 1, "roughnessFactor": 0.14}}, {"name": "satin finish aluminum-3", "normalTexture": {"index": 0, "scale": 0.3}, "pbrMetallicRoughness": {"baseColorFactor": [0.866667, 0.909804, 1, 1], "metallicFactor": 1, "roughnessFactor": 0.1}}, {"name": "color-2", "pbrMetallicRoughness": {"baseColorFactor": [1, 0, 0, 1], "metallicFactor": 0, "roughnessFactor": 0.17}}, {"name": "color-3", "pbrMetallicRoughness": {"baseColorFactor": [0, 0, 0, 1], "metallicFactor": 0, "roughnessFactor": 0.17}}, {"name": "satin finish aluminum", "normalTexture": {"index": 0, "scale": 0.3}, "pbrMetallicRoughness": {"baseColorFactor": [0.0392157, 0, 0.278431, 1], "metallicFactor": 1, "roughnessFactor": 0.1}}, {"name": "satin finish stainless steel", "normalTexture": {"index": 0, "scale": 0.3}, "pbrMetallicRoughness": {"baseColorFactor": [1, 0.94902, 0.909804, 1], "metallicFactor": 1, "roughnessFactor": 0.1}}, {"name": "satin finish stainless steel-1", "normalTexture": {"index": 0, "scale": 0.3}, "pbrMetallicRoughness": {"baseColorFactor": [1, 0.94902, 0.909804, 1], "metallicFactor": 1, "roughnessFactor": 0.1}}, {"name": "satin finish stainless steel-3", "normalTexture": {"index": 0, "scale": 0.3}, "pbrMetallicRoughness": {"baseColorFactor": [1, 0.94902, 0.909804, 1], "metallicFactor": 1, "roughnessFactor": 0.1}}, {"name": "light grey high gloss plastic-5", "pbrMetallicRoughness": {"baseColorFactor": [0.25098, 0.25098, 0.25098, 1], "metallicFactor": 0, "roughnessFactor": 0.17}}, {"name": "satin finish stainless steel-2", "normalTexture": {"index": 0, "scale": 0.3}, "pbrMetallicRoughness": {"baseColorFactor": [1, 0.94902, 0.909804, 1], "metallicFactor": 1, "roughnessFactor": 0.1}}, {"name": "light grey high gloss plastic-2", "pbrMetallicRoughness": {"baseColorFactor": [0.25098, 0.25098, 0.25098, 1], "metallicFactor": 0, "roughnessFactor": 0.17}}, {"name": "light grey high gloss plastic-6", "pbrMetallicRoughness": {"baseColorFactor": [1, 1, 0, 1], "metallicFactor": 0, "roughnessFactor": 0.17}}, {"name": "color", "pbrMetallicRoughness": {"baseColorFactor": [1, 0, 0, 1], "metallicFactor": 0, "roughnessFactor": 0.17}}, {"name": "light grey high gloss plastic", "pbrMetallicRoughness": {"baseColorFactor": [0.25098, 0.25098, 0.25098, 1], "metallicFactor": 0, "roughnessFactor": 0.17}}, {"name": "color-13", "pbrMetallicRoughness": {"baseColorFactor": [0.752941, 0.752941, 0.752941, 1], "metallicFactor": 0, "roughnessFactor": 0.17}}, {"name": "color-12", "pbrMetallicRoughness": {"baseColorFactor": [1, 1, 1, 1], "metallicFactor": 0, "roughnessFactor": 0.17}}, {"name": "color-14", "pbrMetallicRoughness": {"baseColorFactor": [0.501961, 0.501961, 0.501961, 1], "metallicFactor": 0, "roughnessFactor": 0.17}}, {"name": "light grey high gloss plastic-3", "pbrMetallicRoughness": {"baseColorFactor": [0.494118, 0.494118, 0.494118, 1], "metallicFactor": 0, "roughnessFactor": 0.17}}, {"name": "color-5", "pbrMetallicRoughness": {"baseColorFactor": [1, 1, 0.501961, 1], "metallicFactor": 0, "roughnessFactor": 0.17}}, {"name": "light grey high gloss plastic-4", "pbrMetallicRoughness": {"baseColorFactor": [0.752941, 0.752941, 0.752941, 1], "metallicFactor": 0, "roughnessFactor": 0.17}}, {"name": "color-6", "pbrMetallicRoughness": {"baseColorFactor": [0.752941, 0.752941, 0, 1], "metallicFactor": 0, "roughnessFactor": 0.17}}, {"name": "color-7", "pbrMetallicRoughness": {"baseColorFactor": [0, 0, 0, 1], "metallicFactor": 0, "roughnessFactor": 0.17}}, {"name": "color-8", "pbrMetallicRoughness": {"baseColorFactor": [1, 1, 1, 1], "metallicFactor": 0, "roughnessFactor": 0.17}}, {"name": "light grey high gloss plastic-1", "pbrMetallicRoughness": {"baseColorFactor": [0.494118, 0.494118, 0.494118, 1], "metallicFactor": 0, "roughnessFactor": 0.17}}, {"name": "color-1", "pbrMetallicRoughness": {"baseColorFactor": [0.25098, 0.25098, 0.25098, 1], "metallicFactor": 0, "roughnessFactor": 0.17}}, {"name": "white-pw-mt11050", "normalTexture": {"index": 4, "scale": 0.3}, "pbrMetallicRoughness": {"baseColorFactor": [1, 0, 0, 1], "metallicFactor": 0, "roughnessFactor": 1}}, {"name": "color-11", "pbrMetallicRoughness": {"baseColorFactor": [1, 1, 1, 1], "metallicFactor": 0, "roughnessFactor": 0.17}}, {"name": "color-9", "pbrMetallicRoughness": {"baseColorFactor": [0.752941, 0.752941, 0.752941, 1], "metallicFactor": 0, "roughnessFactor": 0.17}}, {"name": "color-10", "pbrMetallicRoughness": {"baseColorFactor": [0.25098, 0.25098, 0.25098, 1], "metallicFactor": 0, "roughnessFactor": 0.17}}], "textures": [{"sampler": 0, "source": 0, "name": "n_satinwood_n.dds"}, {"sampler": 1, "source": 1, "name": "carbon fiber eproxy.jpg"}, {"sampler": 2, "source": 2, "name": "n_carbon fiber eproxy.jpg"}, {"sampler": 3, "source": 3, "name": "n_carbon fiber eproxy.jpg"}, {"sampler": 4, "source": 4, "name": "n_PlasticPolystyrene_normalmap.dds"}], "images": [{"uri": "n_satinwood_n.dds"}, {"uri": "carbon fiber eproxy.jpg"}, {"uri": "n_carbon fiber eproxy.jpg"}, {"uri": "n_carbon fiber eproxy.jpg"}, {"uri": "n_PlasticPolystyrene_normalmap.dds"}], "samplers": [{"magFilter": 9729, "minFilter": 9729, "wrapS": 10497, "wrapT": 10497, "name": "DefaultSampler"}, {"magFilter": 9729, "minFilter": 9729, "wrapS": 10497, "wrapT": 10497, "name": "DefaultSampler"}, {"magFilter": 9729, "minFilter": 9729, "wrapS": 10497, "wrapT": 10497, "name": "DefaultSampler"}, {"magFilter": 9729, "minFilter": 9729, "wrapS": 10497, "wrapT": 10497, "name": "DefaultSampler"}, {"magFilter": 9729, "minFilter": 9729, "wrapS": 10497, "wrapT": 10497, "name": "DefaultSampler"}], "bufferViews": [{"buffer": 0, "byteOffset": 0, "byteLength": 1160}, {"buffer": 0, "byteOffset": 1160, "byteLength": 1160}, {"buffer": 0, "byteOffset": 2320, "byteLength": 11916}, {"buffer": 0, "byteOffset": 14236, "byteLength": 1160}, {"buffer": 0, "byteOffset": 15396, "byteLength": 1160}, {"buffer": 0, "byteOffset": 16556, "byteLength": 1460}, {"buffer": 0, "byteOffset": 18016, "byteLength": 1460}, {"buffer": 0, "byteOffset": 19476, "byteLength": 1460}, {"buffer": 0, "byteOffset": 20936, "byteLength": 1460}, {"buffer": 0, "byteOffset": 22396, "byteLength": 6756}, {"buffer": 0, "byteOffset": 29152, "byteLength": 1160}, {"buffer": 0, "byteOffset": 30312, "byteLength": 33600}, {"buffer": 0, "byteOffset": 63912, "byteLength": 1160}, {"buffer": 0, "byteOffset": 65072, "byteLength": 3008}, {"buffer": 0, "byteOffset": 68080, "byteLength": 3136}, {"buffer": 0, "byteOffset": 71216, "byteLength": 67048}, {"buffer": 0, "byteOffset": 138264, "byteLength": 75028}, {"buffer": 0, "byteOffset": 213292, "byteLength": 80332}, {"buffer": 0, "byteOffset": 293624, "byteLength": 68076}, {"buffer": 0, "byteOffset": 361700, "byteLength": 77412}, {"buffer": 0, "byteOffset": 439112, "byteLength": 78204}, {"buffer": 0, "byteOffset": 517316, "byteLength": 19328}, {"buffer": 0, "byteOffset": 536644, "byteLength": 12148}, {"buffer": 0, "byteOffset": 548792, "byteLength": 13412}, {"buffer": 0, "byteOffset": 562204, "byteLength": 3008}, {"buffer": 0, "byteOffset": 565212, "byteLength": 3136}, {"buffer": 0, "byteOffset": 568348, "byteLength": 67048}, {"buffer": 0, "byteOffset": 635396, "byteLength": 75028}, {"buffer": 0, "byteOffset": 710424, "byteLength": 80332}, {"buffer": 0, "byteOffset": 790756, "byteLength": 68076}, {"buffer": 0, "byteOffset": 858832, "byteLength": 77412}, {"buffer": 0, "byteOffset": 936244, "byteLength": 78204}, {"buffer": 0, "byteOffset": 1014448, "byteLength": 19328}, {"buffer": 0, "byteOffset": 1033776, "byteLength": 12148}, {"buffer": 0, "byteOffset": 1045924, "byteLength": 13412}, {"buffer": 0, "byteOffset": 1059336, "byteLength": 704}, {"buffer": 0, "byteOffset": 1060040, "byteLength": 3488}, {"buffer": 0, "byteOffset": 1063528, "byteLength": 4168}, {"buffer": 0, "byteOffset": 1067696, "byteLength": 5368}, {"buffer": 0, "byteOffset": 1073064, "byteLength": 5368}, {"buffer": 0, "byteOffset": 1078432, "byteLength": 5936}, {"buffer": 0, "byteOffset": 1084368, "byteLength": 3008}, {"buffer": 0, "byteOffset": 1087376, "byteLength": 3136}, {"buffer": 0, "byteOffset": 1090512, "byteLength": 67048}, {"buffer": 0, "byteOffset": 1157560, "byteLength": 75028}, {"buffer": 0, "byteOffset": 1232588, "byteLength": 80332}, {"buffer": 0, "byteOffset": 1312920, "byteLength": 68076}, {"buffer": 0, "byteOffset": 1380996, "byteLength": 77412}, {"buffer": 0, "byteOffset": 1458408, "byteLength": 78204}, {"buffer": 0, "byteOffset": 1536612, "byteLength": 19328}, {"buffer": 0, "byteOffset": 1555940, "byteLength": 12148}, {"buffer": 0, "byteOffset": 1568088, "byteLength": 5936}, {"buffer": 0, "byteOffset": 1574024, "byteLength": 6656}, {"buffer": 0, "byteOffset": 1580680, "byteLength": 972}, {"buffer": 0, "byteOffset": 1581652, "byteLength": 5784}, {"buffer": 0, "byteOffset": 1587436, "byteLength": 8108}, {"buffer": 0, "byteOffset": 1595544, "byteLength": 972}, {"buffer": 0, "byteOffset": 1596516, "byteLength": 972}, {"buffer": 0, "byteOffset": 1597488, "byteLength": 880}, {"buffer": 0, "byteOffset": 1598368, "byteLength": 1840}, {"buffer": 0, "byteOffset": 1600208, "byteLength": 13156}, {"buffer": 0, "byteOffset": 1613364, "byteLength": 3440}, {"buffer": 0, "byteOffset": 1616804, "byteLength": 1020}, {"buffer": 0, "byteOffset": 1617824, "byteLength": 6072}, {"buffer": 0, "byteOffset": 1623896, "byteLength": 5368}, {"buffer": 0, "byteOffset": 1629264, "byteLength": 972}, {"buffer": 0, "byteOffset": 1630236, "byteLength": 5716}, {"buffer": 0, "byteOffset": 1635952, "byteLength": 5936}, {"buffer": 0, "byteOffset": 1641888, "byteLength": 5720}, {"buffer": 0, "byteOffset": 1647608, "byteLength": 17948}, {"buffer": 0, "byteOffset": 1665556, "byteLength": 640}, {"buffer": 0, "byteOffset": 1666196, "byteLength": 1068}, {"buffer": 0, "byteOffset": 1667264, "byteLength": 3712}, {"buffer": 0, "byteOffset": 1670976, "byteLength": 13412}, {"buffer": 0, "byteOffset": 1684388, "byteLength": 6676}, {"buffer": 0, "byteOffset": 1691064, "byteLength": 8064}, {"buffer": 0, "byteOffset": 1699128, "byteLength": 5936}, {"buffer": 0, "byteOffset": 1705064, "byteLength": 5368}, {"buffer": 0, "byteOffset": 1710432, "byteLength": 5368}, {"buffer": 0, "byteOffset": 1715800, "byteLength": 5368}, {"buffer": 0, "byteOffset": 1721168, "byteLength": 5368}, {"buffer": 0, "byteOffset": 1726536, "byteLength": 3008}, {"buffer": 0, "byteOffset": 1729544, "byteLength": 3136}, {"buffer": 0, "byteOffset": 1732680, "byteLength": 67048}, {"buffer": 0, "byteOffset": 1799728, "byteLength": 75028}, {"buffer": 0, "byteOffset": 1874756, "byteLength": 80332}, {"buffer": 0, "byteOffset": 1955088, "byteLength": 68076}, {"buffer": 0, "byteOffset": 2023164, "byteLength": 77412}, {"buffer": 0, "byteOffset": 2100576, "byteLength": 78204}, {"buffer": 0, "byteOffset": 2178780, "byteLength": 19328}, {"buffer": 0, "byteOffset": 2198108, "byteLength": 12148}, {"buffer": 0, "byteOffset": 2210256, "byteLength": 6676}, {"buffer": 0, "byteOffset": 2216932, "byteLength": 8064}, {"buffer": 0, "byteOffset": 2224996, "byteLength": 6676}, {"buffer": 0, "byteOffset": 2231672, "byteLength": 8064}, {"buffer": 0, "byteOffset": 2239736, "byteLength": 6676}, {"buffer": 0, "byteOffset": 2246412, "byteLength": 8064}, {"buffer": 0, "byteOffset": 2254476, "byteLength": 13412}, {"buffer": 0, "byteOffset": 2267888, "byteLength": 880}, {"buffer": 0, "byteOffset": 2268768, "byteLength": 5368}, {"buffer": 0, "byteOffset": 2274136, "byteLength": 5936}, {"buffer": 0, "byteOffset": 2280072, "byteLength": 5368}, {"buffer": 0, "byteOffset": 2285440, "byteLength": 5368}, {"buffer": 0, "byteOffset": 2290808, "byteLength": 5936}, {"buffer": 0, "byteOffset": 2296744, "byteLength": 5368}, {"buffer": 0, "byteOffset": 2302112, "byteLength": 5368}, {"buffer": 0, "byteOffset": 2307480, "byteLength": 5936}, {"buffer": 0, "byteOffset": 2313416, "byteLength": 5936}, {"buffer": 0, "byteOffset": 2319352, "byteLength": 5936}, {"buffer": 0, "byteOffset": 2325288, "byteLength": 5936}, {"buffer": 0, "byteOffset": 2331224, "byteLength": 5936}, {"buffer": 0, "byteOffset": 2337160, "byteLength": 5936}, {"buffer": 0, "byteOffset": 2343096, "byteLength": 5936}, {"buffer": 0, "byteOffset": 2349032, "byteLength": 6656}, {"buffer": 0, "byteOffset": 2355688, "byteLength": 5784}, {"buffer": 0, "byteOffset": 2361472, "byteLength": 6656}, {"buffer": 0, "byteOffset": 2368128, "byteLength": 6656}, {"buffer": 0, "byteOffset": 2374784, "byteLength": 5784}, {"buffer": 0, "byteOffset": 2380568, "byteLength": 5784}, {"buffer": 0, "byteOffset": 2386352, "byteLength": 5936}, {"buffer": 0, "byteOffset": 2392288, "byteLength": 5936}, {"buffer": 0, "byteOffset": 2398224, "byteLength": 5936}, {"buffer": 0, "byteOffset": 2404160, "byteLength": 8108}, {"buffer": 0, "byteOffset": 2412268, "byteLength": 8108}, {"buffer": 0, "byteOffset": 2420376, "byteLength": 8108}, {"buffer": 0, "byteOffset": 2428484, "byteLength": 13252}, {"buffer": 0, "byteOffset": 2441736, "byteLength": 8760}, {"buffer": 0, "byteOffset": 2450496, "byteLength": 392}, {"buffer": 0, "byteOffset": 2450888, "byteLength": 13656}], "buffers": [{"byteLength": 2464544, "uri": "data.bin"}]}