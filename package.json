{"type": "module", "name": "alemdar-website", "version": "0.1.0", "private": true, "scripts": {"dev": "npm install && next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@designbycode/tailwindcss-text-shadow": "^2.2.1", "@fontsource/inter": "^5.2.5", "@modelcontextprotocol/server-postgres": "^0.6.2", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-tooltip": "^1.2.3", "@react-three/drei": "^10.0.5", "@react-three/fiber": "^9.1.1", "@tanstack/react-query": "^5.72.0", "@vercel/analytics": "^1.5.0", "@vercel/speed-insights": "^1.2.0", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.6.2", "geist": "^1.3.1", "gsap": "^3.12.7", "gsap-trial": "^3.12.7", "lucide-react": "^0.485.0", "next": "15.2.4", "next-themes": "^0.4.6", "ogl": "^1.0.11", "pg": "^8.14.1", "react": "^19.0.0", "react-cookiebot": "^1.0.10", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "react-parallax-tilt": "^1.7.287", "resend": "^4.6.0", "sonner": "^2.0.3", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "three": "^0.175.0", "tw-animate-css": "^1.2.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4.0.17", "@types/istanbul-lib-report": "^3.0.3", "@types/node": "^20", "@types/pg": "^8.11.11", "@types/prop-types": "^15.7.15", "@types/react": "^19", "@types/react-cookiebot": "^1.0.5", "@types/react-dom": "^19", "@types/three": "^0.175.0", "eslint": "^9", "eslint-config-next": "15.2.4", "tailwindcss": "^4.0.17", "typescript": "^5"}}