"use client"

import type React from "react"

import { useState, useEffect, useRef } from "react"
import { motion, useScroll, useTransform, AnimatePresence } from "framer-motion"
import { <PERSON><PERSON>les, ChevronLeft, ChevronRight } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"
import {useRouter} from 'next/navigation';
interface Product {
  id: number
  name: string
  images: string[]
  description: string  // Added description field
  isNew?: boolean
}

interface Category {
  id: string
  name: string
  products: Product[]
}

export default function NewcomersSection() {
  const [activeCategory, setActiveCategory] = useState<string>("")
  const [activeIndex, setActiveIndex] = useState(0)
  const [currentImageIndex, setCurrentImageIndex] = useState<Record<number, number>>({})
  const containerRef = useRef<HTMLDivElement>(null)
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start end", "end start"],
  })

  const opacity = useTransform(scrollYProgress, [0, 0.2, 0.8, 1], [0, 1, 1, 0])
  const scale = useTransform(scrollYProgress, [0, 0.2, 0.8, 1], [0.8, 1, 1, 0.8])
  const router = useRouter();
  // Sample data with multiple categories and product descriptions
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const categories: Category[] = [
    {
      id: "electronics",
      name: "Electronics",
      products: [
        {
          id: 1,
          name: "Faucet Water Purifier",
          description: "Angel Musluk Su Arıtıcı Şimdi Alemdar Teknik’te! Temiz ve sağlıklı su her an elinizin altında Filtre değişimi çok kolay! (6 ayda bir) Kurulum ve teslimat ÜCRETSİZ  Sadece 7000 TL",
          images: [
            "https://www.angelwatersolutions.com/uploads/C120-top.jpg",
            "https://www.angelwatersolutions.com/uploads/C120-bottom.jpg",
            "https://www.angelwatersolutions.com/uploads/C120-f1.jpg"
          ],
        },
        {
          id: 2,
          name: "Portable High Power Searchlight USB Charging",
          description:"",
          images: [
            "https://s.alicdn.com/@sc04/kf/Hc3af7e4d8456492da8e33c2aa9f7dd082.jpg_720x720q50.jpg",
            "https://s.alicdn.com/@sc04/kf/Hcaebebd4e85e4a56b32f0099151eba0fD.jpg_720x720q50.jpg",
            "https://s.alicdn.com/@sc04/kf/Hd8ad7aadd9c842f7bb2fd8d9ddf758ceR.jpg_720x720q50.jpg"
          ],
        },
      ],
    },
    {
      id: "Solar Solution",
      name: "Solar Solution",
      products: [
        {
          id: 1,
          name: "APX ECO 6,2KW-500VDC",
          description: "Apex Eco Series Hybrid inverter provides maximum savings and uninterrupted energy with the ability to use both solar energy and the electrical grid at the same time. Thanks to its easy installation and user-friendly interface, it offers a sustainable, efficient and environmentally friendly energy management experience in your home or workplace.",
          images: [
            "https://electrozirve.com/dacegug/2025/02/%E6%AD%A3%E9%9D%A2-3.png",
            "https://electrozirve.com/dacegug/2025/02/%E5%B7%A6%E4%BE%A7-3.png",
            "https://electrozirve.com/qaqiruv/webp-express/webp-images/uploads/2025/02/%E5%B7%A6%E8%BA%BA-1.png.webp",
            "https://electrozirve.com/qaqiruv/webp-express/webp-images/uploads/2025/02/%E5%BA%95%E8%A7%86%E5%9B%BE-1.png.webp",
          ],
        },
        {
          id: 2,
          name: "APX ECO 8,5KW-500VDC",
          description: "Apex Eco Series Hybrid inverter provides maximum savings and uninterrupted energy with the ability to use both solar energy and the electrical grid at the same time. Thanks to its easy installation and user-friendly interface, it offers a sustainable, efficient and environmentally friendly energy management experience in your home or workplace.",
          images: [
            "https://electrozirve.com/dacegug/2025/02/%E5%B7%A6%E4%BE%A7-3.png",
            "https://electrozirve.com/dacegug/2025/02/%E6%AD%A3%E9%9D%A2-3.png",
            "https://electrozirve.com/qaqiruv/webp-express/webp-images/uploads/2025/02/%E5%B7%A6%E8%BA%BA-1.png.webp",
            "https://electrozirve.com/qaqiruv/webp-express/webp-images/uploads/2025/02/%E5%BA%95%E8%A7%86%E5%9B%BE-1.png.webp",
          ],
        },
        {
          id: 3,
          name: "APX ECO 1,1KW-500VDC",
          description: "Apex Eco Series Hybrid inverter provides maximum savings and uninterrupted energy with the ability to use both solar energy and the electrical grid at the same time. Thanks to its easy installation and user-friendly interface, it offers a sustainable, efficient and environmentally friendly energy management experience in your home or workplace.",
          images: [
            "https://electrozirve.com/qaqiruv/webp-express/webp-images/uploads/2025/02/%E5%B7%A6%E8%BA%BA-1.png.webp",
            "https://electrozirve.com/dacegug/2025/02/%E6%AD%A3%E9%9D%A2-3.png",
            "https://electrozirve.com/dacegug/2025/02/%E5%B7%A6%E4%BE%A7-3.png",
            "https://electrozirve.com/qaqiruv/webp-express/webp-images/uploads/2025/02/%E5%BA%95%E8%A7%86%E5%9B%BE-1.png.webp",
          ],
        },
        {
          id: 4,
          name: "12V 200Ah Jel Akü Apex",
          description: "",
          images: [
            "https://koctas-img.mncdn.com/mnpadding/300/300/ffffff/productimages/5001477439/5001477439_1_MC/9241877413938_1730383328223.png",
            "https://koctas-img.mncdn.com/mnpadding/300/300/ffffff/productimages/5001477437/5001477437_2_MC/9241876201522_1730383326754.png",
            "https://koctas-img.mncdn.com/mnpadding/300/300/ffffff/productimages/5001477437/5001477437_3_MC/9241875972146_1730383326486.png",

          ],
        },
        {
          id: 5,
          name: "12V 200Ah Safepower Jel Akü Apex",
          description: "",
          images: [
            "https://electrozirve.com/dacegug/2025/03/APEX-S1-2.jpg",
            "https://electrozirve.com/dacegug/2025/03/APEX-S2-2.jpg",
            "https://electrozirve.com/dacegug/2025/03/APEX-S4-1.jpg",

          ],
        },
        {
          id: 6,
          name: "Electrozirve 60A DC-DC Charger EC1212-60 ",
          description: "",
          images: [
            "https://electrozirve.com/dacegug/2024/07/DC-DC-60A-2-2.png",
            "https://electrozirve.com/dacegug/2024/07/DC-DC-40A-2.jpeg",
          ],
        },
        {
          id: 7,
          name: "NS1200-12 Apex 1200W Modifiye Sinüs İnverter 12V",
          description: "APEX 12 Volt 1200 Watt Modifiye Sinüs İnverter, güvenilir ve verimli enerji dönüşümü sağlar. Kompakt ve dayanıklı tasarımıyla çeşitli cihazlarınız için mükemmel performans sunar.",
          images: [
            "https://electrozirve.com/qaqiruv/webp-express/webp-images/uploads/2025/03/APEX-12-1.jpg.webp",
            "https://electrozirve.com/dacegug/2025/03/APEX-11-1.jpg",
            "https://electrozirve.com/dacegug/2025/03/AP3D511.jpg",
            "https://electrozirve.com/dacegug/2025/03/APE72D1.jpg",
          ],
        },
        {
          id: 8,
          name: "NS2000-12 Apex 2000W Modifiye Sinüs İnverter 12V",
          description: "APEX 12 Volt 2000 Watt Modifiye Sinüs İnverter, güvenilir ve verimli enerji dönüşümü sağlar. Kompakt ve dayanıklı tasarımıyla çeşitli cihazlarınız için mükemmel performans sunar.",
          images: [
            "https://electrozirve.com/dacegug/2025/03/APEX-14-2.jpg",
            "https://electrozirve.com/dacegug/2025/03/APEX-11-2.jpg",
            "https://electrozirve.com/dacegug/2025/03/APEX-13-2.jpg",

          ],
        },
        {
          id: 9,
          name: "Apex Shiner2410 10A Mppt Solar Charge Controller 12/24V",
          description: "MPPT cihazları, güneş panellerinden en yüksek enerji verimliliğini sağlamak için tasarlanmış akıllı şarj kontrolörleridir.",
          images: [
            "https://electrozirve.com/dacegug/2024/07/kapak3.png",
            "https://electrozirve.com/dacegug/2024/07/kapak1.png",
          ],
        },
        {
          id: 10,
          name: "Apex Shiner2420 20A Mppt Solar Charge Controller 12/24V",
          description: "",
          images: [
           "https://electrozirve.com/dacegug/2024/07/kapak3.png",
            "https://electrozirve.com/dacegug/2024/07/kapak1.png",
          ],
        },
        {
          id: 11,
          name: "NP2410 NP Power 24V 10A Akü Şarj Cihazı",
          description: "NP Power 24 Volt 10 Amper Akü Şarj Cihazı, Alternatif akım (AC) enerjisi, bir akü şarj cihazı aracılığıyla doğru akıma (DC) dönüştürülerek bataryaların şarj edilmesini sağlar. Bu işlem, bataryaların ihtiyaç duyduğu uygun voltaj ve akım değerlerinin elde edilmesine yardımcı olur.",
          images: [
            "https://electrozirve.com/dacegug/2025/03/NP-POW1-8.jpg",
            "https://electrozirve.com/dacegug/2025/03/NPD6461.jpg",
            "https://electrozirve.com/dacegug/2025/03/NP-POW3-8.jpg",
          ],
        },
        {
          id: 12,
          name: "Electrozirve 20A Transfer Switch",
          description: "The transfer switch automatically directs energy to the backup source during power outages. This device provides a safe and smooth power transition by providing uninterrupted energy.",
          images: [
            "https://electrozirve.com/dacegug/2024/07/20A-1.png",
            "https://electrozirve.com/dacegug/2024/07/20A-2.png",
            "https://electrozirve.com/dacegug/2024/07/20A-3.png",
          ],
        },
        {
          id: 13,
          name: "Köşe Braket Beyaz",
          description: "",
          images: [
            "https://electrozirve.com/dacegug/2024/10/40.png",
            "https://electrozirve.com/dacegug/2024/10/37.png",
          ],
        },
        {
          id: 14,
          name: "Orta Braket Beyaz",
          description: "",
          images: [
            "https://electrozirve.com/dacegug/2024/10/41.png",
            "https://electrozirve.com/dacegug/2024/10/34.png",
          ],
        },
        {
          id: 15,
          name: "Kablo Kapağı Beyaz",
          description: "",
          images: [
            "https://electrozirve.com/dacegug/2024/10/2.png",
            "https://electrozirve.com/dacegug/2024/10/4.png",
          ],
        },
        {
          id: 16,
          name: "6’lı USB’li Sigortalı Karavan Paneli",
          description: "",
          images: [
            "https://electrozirve.com/dacegug/2025/04/6li-USBli-Sigortali-Karavan-Paneli.jpg",
          ],
        },
        {
          id: 17,
          name: "Led Dış 10cm Gri Sanel SMZLS12Q12W",
          description: "Alüminyum soğutuculu kasaya sahiptir. IP67 su geçirmez özelliktedir. 1 adet fiyatıdır",
          images: [
            "https://ideacdn.net/idea/lr/11/myassets/products/318/sanel-karavan-dis-aydinlatma.jpg?revision=1705581762",
            ],
        },
      ],
    },
    {
      id: "Arduino",
      name: "Arduino",
      products: [
        {
          id: 1,
          name: "Nazgul 5030 Tri-blade Prop",
          description: "This is a custom propeller for XING 2005/ XING21806 motors, does not fit for other motor.",
          images: [
            "/arduinonewcomers/p1.png",
            "/arduinonewcomers/p2.png",
            "/arduinonewcomers/p3.png"
          ],
        },
        {
          id: 2,
          name: "L298N Motor Driver Controller Board Module Stepper Motor DC Dual H-Bridge, suitable for motors, robots, smart cars",
          description: "",
          images: [
            "https://m.media-amazon.com/images/I/51tBpP9BopL._SX522_.jpg",
            "https://m.media-amazon.com/images/I/51EuIwDlOlL._SX342_.jpg",
            "https://m.media-amazon.com/images/I/51Xdq6cYhDL._SX342_.jpg",
            "https://m.media-amazon.com/images/I/51RGtE9tLjL._SX342_.jpg"
          ],
        },
        {
          id: 3,
          name: "ESP32 Development Board | USB Type-C 38-pin",
          description: "The ESP32 Development Board with USB Type-C is a versatile and compact microcontroller board designed for embedded systems and IoT (Internet of Things) applications.",
          images: [
            "https://techtoast.co.za/cdn/shop/files/download_7_9d7b74a8-227c-4e59-868e-ed63b84f8dc6.jpg?v=1719538175",
          ],
        },
        {
          id: 4,
          name: "UNO R3 Resmi ATMEGA16U2/UNO+WiFi R3 MEGA328P",
          description: "",
          images: [
            "https://ae-pic-a1.aliexpress-media.com/kf/Sddf20b91dfea44c38be4812db0bc0758A.jpeg_960x960q75.jpeg_.avif",
            "https://ae-pic-a1.aliexpress-media.com/kf/Sbd3d66a91bc04c3fab8d91822458dd6c1.jpeg_960x960q75.jpeg_.avif",
            "https://ae-pic-a1.aliexpress-media.com/kf/S8fe2e834bbf648bab5a31f346af2b9f86.jpeg_960x960q75.jpeg_.avif"
          ],
        },
      ],
    },
    // Sound Systems category commented out for later use
    /*
    {
      id: "Sound Systems",
      name: "Sound Systems",
      products: [
        {
          id: 1,
          name: "Designer Sunglasses",
          description:"",
          images: [
            "/placeholder.svg?height=600&width=900&text=Sunglasses+1",
            "/placeholder.svg?height=600&width=900&text=Sunglasses+2",
          ],
        },
        {
          id: 2,
          name: "Leather Crossbody Bag",
          description:"",
          images: [
            "/placeholder.svg?height=600&width=900&text=Bag+1",
            "/placeholder.svg?height=600&width=900&text=Bag+2",
            "/placeholder.svg?height=600&width=900&text=Bag+3",
          ],
        },
      ],
    },
    */
  ]

  // Set initial active category
  useEffect(() => {
    if (categories.length > 0 && !activeCategory) {
      setActiveCategory(categories[0].id)
    }
  }, [categories, activeCategory])

  // Get current category products
  const currentProducts = categories.find((cat) => cat.id === activeCategory)?.products || []

  // Auto-rotate products
  // useEffect(() => {
  //   const interval = setInterval(() => {
  //     if (currentProducts.length > 0) {
  //       setActiveIndex((prev) => (prev + 1) % currentProducts.length)
  //     }
  //   }, 5000)
  //   return () => clearInterval(interval)
  // }, [currentProducts])

  // Initialize current image index for each product
  useEffect(() => {
    const initialImageIndices: Record<number, number> = {}
    categories.forEach((category) => {
      category.products.forEach((product) => {
        initialImageIndices[product.id] = 0
      })
    })
    setCurrentImageIndex(initialImageIndices)
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  // Reset image indices when categories change
  useEffect(() => {
    // Reset all image indices to 0 when category changes
    const resetImageIndices: Record<number, number> = {}
    categories.forEach((category) => {
      category.products.forEach((product) => {
        resetImageIndices[product.id] = 0
      })
    })
    setCurrentImageIndex(resetImageIndices)
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeCategory])

  // Reset active index when changing category
  useEffect(() => {
    setActiveIndex(0)
  }, [activeCategory])

  // Helper function to find a product by ID
  const findProductById = (productId: number): Product | undefined => {
    for (const category of categories) {
      const product = category.products.find(p => p.id === productId)
      if (product) return product
    }
    return undefined
  }

  const nextImage = (productId: number, e?: React.MouseEvent) => {
    e?.stopPropagation()

    // Find the product
    const product = findProductById(productId)

    // Safety checks
    if (!product || !product.images || product.images.length <= 1) return

    // Get current index with fallback to 0
    const currentIdx = currentImageIndex[productId] ?? 0

    // Calculate next index with safe wrapping
    let nextIdx = 0
    if (currentIdx >= 0 && currentIdx < product.images.length - 1) {
      nextIdx = currentIdx + 1
    } else {
      nextIdx = 0 // Wrap to beginning
    }

    // Update state
    setCurrentImageIndex(prev => {
      const newState = { ...prev }
      newState[productId] = nextIdx
      return newState
    })
  }

  const prevImage = (productId: number, e?: React.MouseEvent) => {
    e?.stopPropagation()

    // Find the product
    const product = findProductById(productId)

    // Safety checks
    if (!product || !product.images || product.images.length <= 1) return

    // Get current index with fallback to 0
    const currentIdx = currentImageIndex[productId] ?? 0

    // Calculate previous index with safe wrapping
    let prevIdx = 0
    if (currentIdx > 0) {
      prevIdx = currentIdx - 1
    } else {
      prevIdx = product.images.length - 1 // Wrap to end
    }

    // Update state
    setCurrentImageIndex(prev => {
      const newState = { ...prev }
      newState[productId] = prevIdx
      return newState
    })
  }

  return (
    <motion.section
      ref={containerRef}
      style={{ opacity, scale }}
      className="relative py-20 overflow-hidden bg-gradient-to-br from-black to-slate-900 text-white"
    >
      {/* Background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-24 -left-24 w-64 h-64 rounded-full bg-purple-500/10 blur-3xl"></div>
        <div className="absolute top-1/2 -right-24 w-80 h-80 rounded-full bg-blue-500/10 blur-3xl"></div>
        <div className="absolute -bottom-24 left-1/3 w-72 h-72 rounded-full bg-pink-500/10 blur-3xl"></div>
      </div>

      {/* Main navigation arrows */}
      <button
        onClick={() => setActiveIndex((prev) => (prev - 1 + currentProducts.length) % currentProducts.length)}
        className="absolute left-4 md:left-8 top-1/2 -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white rounded-full p-3 transition-all z-30 cursor-pointer"
        aria-label="Previous product"
      >
        <ChevronLeft className="h-8 w-8" />
      </button>

      <button
        onClick={() => setActiveIndex((prev) => (prev + 1) % currentProducts.length)}
        className="absolute right-4 md:right-8 top-1/2 -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white rounded-full p-3 transition-all z-30 cursor-pointer"
        aria-label="Next product"
      >
        <ChevronRight className="h-8 w-8" />
      </button>

      {/* Content container */}
      <div className="container relative mx-auto px-4 max-w-7xl">
        {/* Header */}
        <div className="mb-12 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <Badge variant="outline" className="mb-4 border-purple-400 text-purple-400 px-4 py-1 text-sm">
              <Sparkles className="mr-1 h-3 w-3" /> Just Arrived
            </Badge>
            <h2 className="text-4xl md:text-5xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-purple-400 via-pink-500 to-blue-500">
              Discover What&apos;s New
            </h2>
            <p className="text-slate-300 max-w-2xl mx-auto">Explore our latest arrivals across multiple categories</p>
          </motion.div>
        </div>

        {/* Category navigation */}
        <div className="mb-12">
          <div className="flex flex-wrap justify-center gap-3">
            {categories.map((category) => (
              <Button
                key={category.id}
                variant={activeCategory === category.id ? "default" : "outline"}
                className={cn(
                  "rounded-full px-6",
                  activeCategory === category.id
                    ? "bg-purple-600 hover:bg-purple-700"
                    : "border-purple-500/50 text-purple-400 hover:bg-purple-500/10 hover:text-white",
                )}
                onClick={() => setActiveCategory(category.id)}
              >
                {category.name}
              </Button>
            ))}
          </div>
        </div>

        {/* Products showcase */}
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-8 items-center">
          {/* Product info */}
          <div className="lg:col-span-4 order-2 lg:order-1">
            {currentProducts.length > 0 && (
              <AnimatePresence mode="wait">
                <motion.div
                  key={`${activeCategory}-${activeIndex}`}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                  transition={{ duration: 0.5 }}
                  className="space-y-6"
                >
                  <Badge className="bg-purple-500 hover:bg-purple-600 px-3 py-1">
                    {categories.find((cat) => cat.id === activeCategory)?.name}
                  </Badge>
                  <h3 className="text-3xl md:text-4xl font-bold">{currentProducts[activeIndex]?.name}</h3>
                  <p className="text-slate-300">
                    {currentProducts[activeIndex]?.description}
                  </p>
                  <div className="flex flex-wrap gap-4">
                    <Button onClick={() => router.push("#contact")}
                    className="bg-purple-600 hover:bg-purple-700 text-white">Contact Shop</Button>
                    <Button
                      onClick={() => {
                        // Redirect based on active category
                        switch (activeCategory) {
                          case "Solar Solution":
                            router.push("/products/categories/Solar");
                            break;
                          case "Arduino":
                            router.push("/products/categories/arduino");
                            break;
                          case "electronics":
                            router.push("/products/categories/electronics");
                            break;
                          default:
                            router.push("#contact");
                            break;
                        }
                      }}
                      className="bg-purple-600 hover:bg-purple-700 text-white">
                      Discover all products
                    </Button>
                  </div>
                </motion.div>
              </AnimatePresence>
            )}
          </div>

          {/* Product cards */}
          <div className="lg:col-span-8 order-1 lg:order-2">
            <div className="relative h-[500px]">
              {currentProducts.map((product, index) => (
                <motion.div
                  key={product.id}
                  className={cn("absolute cursor-pointer transition-all duration-500")}
                  style={{
                    width: "600px",
                    height: "400px",
                    left: "calc(50% - 16rem)",
                    top: "calc(50% - 10rem)",
                    zIndex: index === activeIndex ? 10 : 0,
                    opacity: index === activeIndex ? 1 : 0,
                    pointerEvents: index === activeIndex ? "auto" : "none"
                  }}
                  onClick={() => setActiveIndex(index)}
                >
                  <div className="relative h-full w-full overflow-hidden">
                    {/* Image - No overlays */}
                    <AnimatePresence mode="wait">
                      {product.images && product.images.length > 0 ? (
                        <motion.img
                          key={`${product.id}-${currentImageIndex[product.id] ?? 0}`}
                          src={product.images[Math.min(currentImageIndex[product.id] ?? 0, product.images.length - 1)]}
                          alt={product.name}
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          exit={{ opacity: 0 }}
                          transition={{ duration: 0.3 }}
                          className="w-full h-full object-contain rounded-2xl"
                        />
                      ) : (
                        <motion.div
                          key={`${product.id}-no-image`}
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          exit={{ opacity: 0 }}
                          transition={{ duration: 0.3 }}
                          className="w-full h-full flex items-center justify-center bg-slate-800/50 rounded-lg"
                        >
                          <p className="text-white text-lg">No image available</p>
                        </motion.div>
                      )}
                    </AnimatePresence>

                    {/* Image navigation controls - only show for active product */}
                    {index === activeIndex && product.images && product.images.length > 1 && (
                      <>
                        <button
                          onClick={(e) => prevImage(product.id, e)}
                          className="absolute left-4 top-1/2 -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white rounded-full p-3 transition-all"
                          aria-label="Previous image"
                        >
                          <ChevronLeft className="h-8 w-8" />
                        </button>
                        <button
                          onClick={(e) => nextImage(product.id, e)}
                          className="absolute right-4 top-1/2 -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white rounded-full p-3 transition-all"
                          aria-label="Next image"
                        >
                          <ChevronRight className="h-8 w-8" />
                        </button>

                        {/* Image indicators */}
                        <div className="absolute bottom-4 left-1/2 -translate-x-1/2 flex space-x-3">
                          {product.images.map((_, imgIndex) => (
                            <div
                              key={imgIndex}
                              className={`w-3 h-3 rounded-full transition-all ${
                                imgIndex === (currentImageIndex[product.id] ?? 0) ? "bg-white w-6" : "bg-white/50"
                              }`}
                              onClick={(e) => {
                                e.stopPropagation();
                                setCurrentImageIndex(prev => ({
                                  ...prev,
                                  [product.id]: imgIndex
                                }));
                              }}
                            />
                          ))}
                        </div>
                      </>
                    )}
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </div>

        {/* Bottom navigation */}
        <div className="mt-12 flex justify-center">
          <div className="flex space-x-3">
            {currentProducts.map((_, index) => (
              <button
                key={index}
                className={`w-3 h-3 rounded-full transition-all duration-300 ${
                  index === activeIndex ? "w-10 bg-purple-500" : "bg-slate-600"
                }`}
                onClick={() => setActiveIndex(index)}
              />
            ))}
          </div>
        </div>
      </div>
    </motion.section>
  )
}
