"use client"

import { useState } from "react"
import Image from "next/image"
import { motion } from "framer-motion"
import { ArrowRight, Package, DollarSign } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"
import ProductDetailSheet from "./productsdetails"
import ProductCart from "./product-cart"
import { FaHeart } from "react-icons/fa";
import { IoMdCart } from "react-icons/io";

interface ProductCardProps {
  title: string
  description?: string
  image: string
  category: string
  section?: "arduino" | "solar" | "electronics" | "sound"
  index: number
  quantity: number
  price: number | null
  alt?: string
}

export default function ProductCard({
  title,
  description,
  image,
  category,
  section = "arduino",
  index,
  quantity,
  price,
  alt
}: ProductCardProps) {
  const [isHovered, setIsHovered] = useState(false)
  const [isSheetOpen, setIsSheetOpen] = useState(false)
  const [isCartOpen, setIsCartOpen] = useState(false)
  const [showOutOfStockModal, setShowOutOfStockModal] = useState(false);
  const { addToCart } = require("@/components/cartcontext").useCart();

  const getCategoryColor = (section: string) => {
    switch (section) {
      case "electronics":
        return "bg-blue-500/10 text-blue-500 border-blue-500/20"
      case "solar":
        return "bg-green-500/10 text-green-500 border-green-500/20"
      case "arduino":
        return "bg-purple-500/10 text-purple-500 border-purple-500/20"
      case "sound":
        return "bg-red-500/10 text-red-500 border-red-500/20"
      default:
        return "bg-gray-500/10 text-gray-500 border-gray-500/20"
    }
  }


  const formatPrice = (price: number | string | null) => {
    if (price === null || price === undefined) return '0.00';
    const numPrice = typeof price === 'string' ? parseFloat(price) : price;
    return isNaN(numPrice) ? '0.00' : numPrice.toFixed(2);
  }

  return (
    <>
      <motion.div
        className="product-card h-full"
        initial={{ opacity: 0, y: 50 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: index * 0.1 }}
      >
        <Card
          className="overflow-hidden bg-gray-900 py-0  border-gray-800 transition-all duration-300 hover:border-gray-700 dark:bg-gray-700 h-full flex flex-col"
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
        >
          <div className="relative h-48 w-full overflow-hidden flex-shrink-0">
            {/* Blurred background for empty spaces */}
            <Image
              src={image || "/placeholder.svg"}
              alt={alt || title}
              fill
              sizes="100vw"
              className={cn(
                "object-cover w-full h-full absolute top-0 left-0 blur-md scale-105 z-0",
                !image ? "opacity-40" : "opacity-60"
              )}
              style={{ zIndex: 0 }}
            />
            {/* Main image on top, contained */}
            <Image
              src={image || "/placeholder.svg"}
              alt={alt || title}
              fill
              sizes="100vw"
              className={cn(
                "object-contain w-full h-full transition-transform duration-500 z-10",
                isHovered ? "scale-105" : "scale-100"
              )}
              style={{ zIndex: 10 }}
            />
            <div className="absolute inset-0 bg-gradient-to-t from-gray-900 to-transparent opacity-60 dark:bg-gradient-to-m dark:from-gray dark:to-gray-600 dark:opacity-100 z-20"></div>
            <Badge className={cn("absolute top-3 left-3 border z-30", getCategoryColor(section))}>
                {category.charAt(0).toUpperCase() + category.slice(1)}
            </Badge>
          </div>
          <CardContent className="p-5 flex flex-col flex-grow">
            <h3 className="text-xl font-bold mb-2 text-white line-clamp-2">{title}</h3>
            <p className="text-gray-400 mb-4 line-clamp-3 flex-grow">{description}</p>
            <div className="flex items-center gap-4 mb-4">
              <div className="flex items-center gap-2 text-gray-300">
                <Package className="h-4 w-4 text-blue-400" />
                <span>{quantity} in stock</span>
              </div>
              <div className="flex items-center gap-2 text-gray-300">
                <DollarSign className="h-4 w-4 text-green-400" />
                <span className="font-semibold">${formatPrice(price)}</span>
              </div>
            </div>
            <div className="flex justify-between items-center mt-auto">
              <Button
                variant="ghost"
                size="sm"
                className="text-blue-400 hover:text-blue-300 hover:bg-blue-900/20 p-0"
                onClick={() => setIsSheetOpen(true)}
              >
                View Details <ArrowRight className="ml-1 h-4 w-4" />
              </Button>
              <div className="flex space-x-2">
                <Button variant="ghost" size="icon" className="h-8 w-8 hover:bg-transparent text-gray-400 hover:text-rose-500 ">
                  <FaHeart className="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() =>
                    quantity < 1
                      ? setShowOutOfStockModal(true)
                      : (addToCart({
                          id: `${title}-${index}`,
                          name: title,
                          price: price ?? 0,
                          quantity: 1,
                          image: image,
                        }), setIsCartOpen(true))
                  }
                  className="h-8 w-8 hover:bg-transparent text-gray-400 hover:text-blue-400"
                >
                  <IoMdCart className="h-6 w-6 " />
                </Button>
      {/* Out of Stock Modal */}
      {showOutOfStockModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/85 bg-opacity-50">
          <div className="bg-white dark:bg-gray-700 rounded-lg shadow-lg p-8 max-w-sm w-full text-center">
            <h2 className="text-xl font-bold mb-4 text-red-600">Out of Stock</h2>
            <p className="mb-6 text-gray-700 dark:text-gray-300">Sorry, this product is currently out of stock.</p>
            <Button className="bg-red-600 hover:bg-red-700 text-white w-full" onClick={() => setShowOutOfStockModal(false)}>
              Close
            </Button>
          </div>
        </div>
      )}
                <ProductCart open={isCartOpen} onOpenChange={setIsCartOpen} />
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      <ProductDetailSheet
        product={{
          title,
          description,
          image,
          category,
          section,
          index,
          quantity,
          price,
          alt
        }}
        open={isSheetOpen}
        onOpenChange={setIsSheetOpen}
      />
    </>
  )
}
