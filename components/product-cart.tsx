'use client'

import {
  Sheet,
  She<PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  She<PERSON><PERSON><PERSON><PERSON>,
  Sheet<PERSON>it<PERSON>
} from "@/components/ui/sheet"
import { Button } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import Image from "next/image"
import { useCart } from "@/components/cartcontext"

interface ProductCartProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

const ProductCart: React.FC<ProductCartProps> = ({ open, onOpenChange }) => {
  const { increment, decrement, cart } = useCart(); // ✅ FIXED

  const total = cart.reduce((acc, item) => acc + item.price * item.quantity, 0);

  return (
  <Sheet open={open} onOpenChange={onOpenChange}>
    <SheetContent className="w-[350px] sm:w-[400px] md:w-[500px] lg:w-[900px] ">
      <SheetHeader>
        <SheetTitle className="text-2xl sm:text-3xl text-center sm:text-left p-3">
          My Cart
        </SheetTitle>
        <SheetDescription className="text-sm sm:text-md text-center sm:text-left px-2">
          You&apos;re almost there! Just one step away from completing your order.
        </SheetDescription>
      </SheetHeader>

      <div className="mt-6 space-y-4 max-h-[60vh] overflow-y-auto pr-2">
        {cart.length === 0 ? (
          <p className="text-center">Your cart is empty.</p>
        ) : (
          cart.map((item) => (
            <div key={item.id} className="flex items-center gap-4 px-2 sm:px-4">
              <div className="relative w-14 h-14 sm:w-16 sm:h-16 border rounded-md bg-white">
                {item.image && (
                  <Image
                    src={item.image}
                    alt={item.name}
                    fill
                    className="p-1 object-contain"
                  />
                )}
              </div>
              <div className="flex-1">
                <p className="font-medium text-sm sm:text-base">{item.name}</p>
                <p className="text-xs text-muted-foreground flex items-center gap-2">
                  Quantity:
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-6 w-6 dark:text-gray-400"
                    onClick={() => decrement(item.id)}
                  >
                    -
                  </Button>
                  {item.quantity}
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-6 w-6 dark:text-gray-400"
                    onClick={() => increment(item.id)}
                  >
                    +
                  </Button>
                </p>
              </div>
              <div className="text-sm font-semibold text-blue-600 whitespace-nowrap">
                ${(item.price * item.quantity).toFixed(2)}
              </div>
            </div>
          ))
        )}
      </div>

      <Separator className="my-6" />

      <div className="flex justify-between items-center font-semibold text-lg mb-4 px-4">
        <span>Total:</span>
        <span>${total.toFixed(2)}</span>
      </div>

      <Button
        className="w-[90%] mx-auto mt-auto mb-6 bg-gradient-to-r from-blue-700 to-violet-700 border-2 hover:from-blue-700/50 hover:to-violet-700/50 border-blue-950 text-white dark:text-white"
        size="lg"
        disabled={cart.length === 0}
      >
        Go to Payment
      </Button>
    </SheetContent>
  </Sheet>
)

}
export default ProductCart;