'use client'

import { ThemeProvider } from "next-themes"
import { QueryClient, QueryClientProvider } from "@tanstack/react-query"
import { useState } from "react"
import {CartProvider} from "@/components/cartcontext"

export function Provider({ children }: { children: React.ReactNode }) {
  const [queryClient] = useState(() => new QueryClient())

  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider attribute="class" defaultTheme="light" enableSystem={false}>
        <CartProvider> 
          {children}
        </CartProvider>
      </ThemeProvider>
    </QueryClientProvider>
  )
}



