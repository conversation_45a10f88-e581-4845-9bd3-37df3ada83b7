'use client'

import React, { createContext, useContext, useState, ReactNode } from 'react'

interface CartItem {
  id: string
  name: string
  price: number
  quantity: number
  image?: string
}

interface CartContextType {
  cart: CartItem[]
  addToCart: (item: CartItem) => void
  removeFromCart: (id: string) => void
  increment: (id: string) => void
  decrement: (id: string) => void
}

const CartContext = createContext<CartContextType | undefined>(undefined)

export function useCart() {
  const context = useContext(CartContext)
  if (!context) throw new Error('useCart must be used within a CartProvider')
  return context
}
export function CartProvider({ children }: { children: ReactNode }) {
  const [cart, setCart] = useState<CartItem[]>([])
  const addToCart = (item: CartItem) => {
  setCart(prev => {
    const existing = prev.find(p => p.id === item.id)
    const newCart = existing
      ? prev.map(p =>
          p.id === item.id ? { ...p, quantity: p.quantity + item.quantity } : p
        )
      : [...prev, item]

    console.log("Cart updated:", newCart) 
    return newCart
  })
}
  const removeFromCart = (id: string) => {
    setCart(prev => prev.filter(item => item.id !== id))
  }
  const increment = (id: string) => {
    setCart(prev =>
      prev.map(item =>
        item.id === id ? { ...item, quantity: item.quantity + 1 } : item
      )
    )
  }
  const decrement = (id: string) => {
    setCart(prev =>
      prev.flatMap(item => {
        if (item.id === id) {
          if (item.quantity > 1) {
            return { ...item, quantity: item.quantity - 1 }
          } else {
            // quantity will become 0 — remove item
            return []
          }
        }
        return item
      })
    )
  }
  return (
    <CartContext.Provider value={{ cart, addToCart, removeFromCart, increment, decrement }}>
      {children}
    </CartContext.Provider>
  )
}
