
'use client'

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/sheet"
import { <PERSON><PERSON> } from "@/components/ui/button"
import Image from "next/image"
import { Separator } from "@radix-ui/react-separator"
import { useCart } from "@/components/cartcontext" // ✅ make sure this path is correct

interface ProductCardProps {
  title: string
  description?: string
  image: string
  category: string
  section?: "arduino" | "solar" | "electronics" | "sound"
  index: number
  quantity: number
  price: number | null
  alt?: string
}

export default function ProductDetailSheet({
  product,
  open,
  onOpenChange,
}: {
  product: ProductCardProps | null
  open: boolean
  onOpenChange: (open: boolean) => void
}) {
  const { addToCart } = useCart() // ✅ move inside the component

  if (!product) return null


  const handleAddToCart = () => {
    addToCart({
      id: `${product.title}-${product.index}`,
      name: product.title,
      price: product.price ?? 0,
      quantity: 1,
      image: product.image,
    })
    onOpenChange(false)
  }

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent
        className="top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 max-h-[90vh] w-full max-w-2xl rounded-2xl p-1 h-126 bg-gradient-to-r from-purple-600/50 to-blue-600/50 dark:from-violet-400/50 dark:to-blue-700/50 dark:border-black border-blue-600 shadow-2xl"
        side="bottom"
      >
        <div className="rounded-xl h-130 bg-white dark:bg-slate-800">
          <div className="flex flex-col md:flex-row h-full">
            <div className="relative w-65 h-full bg-white rounded-2xl dark:bg-slate-900/50">
              <Image
                src={product.image}
                alt={product.alt || product.title}
                fill
                className="object-contain"
              />
            </div>
            <div className="flex flex-col justify-between p-6 w-full md:w-1/2 space-y-6">
              <div>
                <SheetHeader>
                  <SheetTitle className="text-2xl font-bold text-center">
                    {product.title}
                  </SheetTitle>
                </SheetHeader>

                <div className="mt-3 space-y-4 text-left text-base">
                  <p>
                    <strong>Category:</strong> {product.category}
                  </p>
                    <p className="text-muted-foreground">{product.description}</p>
                

                  <Separator className="mt-10 mb-[-45] ml-3 h-[0.5px] w-full bg-gradient-to-r from-blue-600 to-violet-600 rounded-full" />

                  <p className="mt-14">
                    <strong className="text-blue-600">Price:</strong> $
                    {typeof product.price === "number"
                      ? product.price.toFixed(2)
                      : parseFloat(String(product.price))?.toFixed(2) ?? "0.00"}
                  </p>

                  <p>
                    <strong className="text-red-600">In stock:</strong> {product.quantity} units 
                  </p>
                </div>
              </div>

              <Button
                onClick={handleAddToCart}
                className="w-full mt-auto ml-3 bg-gradient-to-r from-blue-600 to-violet-600 hover:from-blue-600/70 hover:to-violet-600/70 dark:text-white"
                size="lg"
              >
                Add to Cart
              </Button>
            </div>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  )
}
